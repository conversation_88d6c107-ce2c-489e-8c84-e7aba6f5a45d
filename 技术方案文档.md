# ChronoTranslate V3.0 增强技术方案文档

## 1. 整体架构概览

### 1.1 双模式处理架构
```mermaid
graph TD
    A[用户交互层] --> B[平台检测器]
    B --> C[内容分析器]

    C --> D{内容类型判断}
    D -->|有字幕| E[字幕处理管道]
    D -->|无字幕| F[音频处理管道]

    E --> G[翻译引擎]
    F --> H[语音识别引擎]
    H --> G

    G --> I[TTS合成引擎]
    I --> J[播放控制器]

    subgraph "平台适配层"
        K[YouTube适配器]
        L[TikTok适配器]
        M[Bilibili适配器]
        N[通用适配器]
    end

    B --> K
    B --> L
    B --> M
    B --> N
```

### 1.2 核心设计原则
- **双模式支持**: 字幕翻译 + 音频识别
- **平台无关**: 统一接口，易于扩展新平台
- **流水线处理**: 模块化节点，支持并行处理
- **智能降级**: 多引擎支持，自动故障转移
- **零延迟优化**: 预处理和缓存机制

## 2. 核心模块设计

### 2.1 平台适配器模式
```typescript
// 平台适配器基类
abstract class PlatformAdapter {
  abstract matches(url: string): boolean;
  abstract getContentInfo(): Promise<IContentInfo | null>;
  abstract getSubtitles(): Promise<ISubtitleTrack[]>;
  abstract getAudioStream(): Promise<MediaStream | null>;
  abstract injectUI(container: HTMLElement): void;
}

// 平台适配器工厂
class PlatformAdapterFactory {
  static register(type: PlatformType, adapter: PlatformAdapter): void;
  static create(url: string): PlatformAdapter | null;
}
```

### 2.2 内容分析器
```typescript
class ContentAnalyzer {
  async analyzeContent(): Promise<IContentInfo | null>;
  getProcessingStrategy(contentInfo: IContentInfo): ProcessingStrategy;
}

interface ProcessingStrategy {
  type: 'subtitle' | 'audio' | 'hybrid';
  priority: string[];
  fallback: string;
}
```

### 2.3 双模式处理管道

#### 字幕处理管道
```mermaid
graph LR
    A[字幕提取] --> B[字幕同步]
    B --> C[字幕过滤]
    C --> D[翻译处理]
    D --> E[显示渲染]
```

#### 音频处理管道
```mermaid
graph LR
    F[音频捕获] --> G[语音识别]
    G --> H[翻译处理]
    H --> I[实时显示]
```

## 3. 核心接口规范

### 3.1 内容信息接口
```typescript
interface IContentInfo {
  platform: PlatformType;
  contentType: ContentType;
  mediaInfo: IMediaInfo;
  subtitles?: ISubtitleTrack[];
  audioBuffer?: ArrayBuffer;
  metadata?: Record<string, any>;
}

enum ContentType {
  SUBTITLE = 'subtitle',    // 有字幕内容
  AUDIO_ONLY = 'audio_only' // 纯音频内容
}
```

### 3.2 字幕轨道接口
```typescript
interface ISubtitleTrack {
  language: string;
  url?: string;
  content?: string;
  format: 'srt' | 'vtt' | 'ass' | 'json';
  isAutoGenerated: boolean;
}
```

### 3.3 翻译引擎接口
```typescript
interface ITranslationEngine {
  translate(text: string, targetLang: string, sourceLang?: string): Promise<ITranslationResult>;
  getSupportedLanguages(): Promise<string[]>;
  detectLanguage(text: string): Promise<string>;
  isAvailable(): Promise<boolean>;
}
```

### 3.4 语音识别接口
```typescript
interface ISpeechRecognitionService {
  recognize(audioSegment: IAudioSegment): Promise<IRecognitionResult>;
  getSupportedLanguages(): string[];
  setLanguage(language: string): void;
}
```

## 4. 关键流程

### 4.1 媒体处理流程
```mermaid
sequenceDiagram
    participant User
    participant PlaybackController
    participant MediaSniffer
    participant Pipeline
    
    User->>PlaybackController: 启动翻译
    PlaybackController->>MediaSniffer: 请求媒体信息
    MediaSniffer-->>PlaybackController: 返回IMediaInfo
    PlaybackController->>Pipeline: 提交处理任务
    Pipeline-->>PlaybackController: 返回缓冲结果
```

## 5. 性能优化

### 5.1 三级缓冲机制
| 缓冲层级 | 数据类型 | 容量 | 策略 |
|---------|---------|------|-----|
| L1      | 原始媒体 | 3个  | LRU |
| L2      | 翻译文本 | 50句 | FIFO |
| L3      | TTS音频 | 10MB | 时间窗口 |

### 5.2 并行处理模型
```mermaid
graph LR
    A[媒体嗅探] --> B[字幕提取]
    B --> C[翻译处理]
    C --> D[TTS合成]
    
    style B fill:#f9f
    style C fill:#ccf
    style D fill:#cfc
```

## 6. 部署配置

### 6.1 开发环境
```bash
# 依赖安装
npm install --save-dev typescript @types/chrome
# 编译命令
npx tsc --build
```

### 6.2 TS配置要点
```json
{
  "compilerOptions": {
    "lib": ["ES2015", "DOM"],
    "types": ["chrome"],
    "strict": true
  }
}