# Installation
> `npm install --save @types/chrome`

# Summary
This package contains type definitions for chrome (https://developer.chrome.com/docs/extensions).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/chrome.

### Additional Details
 * Last updated: Tue, 01 Jul 2025 19:32:44 GMT
 * Dependencies: [@types/filesystem](https://npmjs.com/package/@types/filesystem), [@types/har-format](https://npmjs.com/package/@types/har-format)

# Credits
These definitions were written by [<PERSON>](https://github.com/matthewkimber), [otiai10](https://github.com/otiai10), [sreimer15](https://github.com/sreimer15), [<PERSON><PERSON><PERSON><PERSON>](https://github.com/Mat<PERSON>ar<PERSON>), [ekinsol](https://github.com/ekinsol), [<PERSON>](https://github.com/echoabstract), [<PERSON><PERSON><PERSON><PERSON>](https://github.com/spasma), [bd<PERSON><PERSON>](https://github.com/bdbai), [<PERSON>](https://github.com/<PERSON>ian), [userTim](https://github.com/usertim), [Idan Zeierman](https://github.com/idan315), [Nicolas Rodriguez](https://github.com/nicolas377), [Ido Salomon](https://github.com/idosal), [Federico Brigante](https://github.com/fregante), and [Erwan Jugand](https://github.com/erwanjugand).
