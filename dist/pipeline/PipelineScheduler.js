import { PipelineNode } from './PipelineNode';
/**
 * 流水线调度器
 */
export class PipelineScheduler {
    constructor() {
        this.pipelines = new Map();
        this.activeTasks = new Set();
    }
    /**
     * 注册流水线
     */
    registerPipeline(name, nodes, concurrency = 3) {
        this.pipelines.set(name, [
            ...nodes,
            new ParallelLimitNode(concurrency), // 添加并行控制节点
        ]);
    }
    /**
     * 执行流水线任务
     */
    async execute(pipelineName, input) {
        const pipeline = this.pipelines.get(pipelineName);
        if (!pipeline) {
            throw new Error(`Pipeline ${pipelineName} not found`);
        }
        let result = input;
        for (const node of pipeline) {
            try {
                const task = node.process(result);
                this.activeTasks.add(task);
                result = await task;
                this.activeTasks.delete(task);
            }
            catch (error) {
                this.handlePipelineError(pipelineName, error);
                throw error;
            }
        }
        return result;
    }
    handlePipelineError(pipelineName, error) {
        console.error(`[${pipelineName}] Pipeline error:`, error);
        // TODO: 实现重试逻辑
    }
}
/**
 * 并行控制节点
 */
class ParallelLimitNode extends PipelineNode {
    constructor(concurrency) {
        super();
        this.concurrency = concurrency;
    }
    async process(inputs) {
        const results = [];
        const queue = [...inputs];
        while (queue.length) {
            const batch = queue.splice(0, this.concurrency);
            const batchResults = await Promise.all(batch);
            results.push(...batchResults);
        }
        return results;
    }
}
