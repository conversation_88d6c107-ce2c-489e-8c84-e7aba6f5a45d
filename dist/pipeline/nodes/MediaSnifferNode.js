import { PipelineNode } from '../PipelineNode';
/**
 * 媒体嗅探节点
 */
export class MediaSnifferNode extends PipelineNode {
    constructor() {
        super(...arguments);
        this.mediaCache = new Map();
    }
    async process(request) {
        // 只处理媒体类型请求
        if (!this.isMediaRequest(request)) {
            return null;
        }
        const mediaInfo = this.createMediaInfo(request);
        this.cacheMediaInfo(request.tabId, mediaInfo);
        return mediaInfo;
    }
    isMediaRequest(request) {
        const contentType = request.responseHeaders?.find(h => h.name.toLowerCase() === 'content-type')?.value;
        return contentType?.includes('video/') || contentType?.includes('audio/') || false;
    }
    createMediaInfo(request) {
        return {
            url: request.url,
            type: this.getMediaType(request),
            initiator: request.initiator || '',
            requestHeaders: this.serializeHeaders(request.responseHeaders),
            tabId: request.tabId,
            cookies: '' // 需要额外获取
        };
    }
    getMediaType(request) {
        if (request.url.endsWith('.m3u8'))
            return 'm3u8';
        if (request.url.includes('.mp4') || request.url.includes('.mov'))
            return 'video';
        return 'audio';
    }
    serializeHeaders(headers) {
        if (!headers)
            return {};
        return headers.reduce((acc, header) => {
            acc[header.name] = header.value || '';
            return acc;
        }, {});
    }
    cacheMediaInfo(tabId, mediaInfo) {
        if (!this.mediaCache.has(tabId)) {
            this.mediaCache.set(tabId, []);
        }
        this.mediaCache.get(tabId)?.push(mediaInfo);
    }
    // 获取标签页的媒体信息
    getMediaForTab(tabId) {
        const mediaList = this.mediaCache.get(tabId);
        return mediaList?.length ? mediaList[0] : null;
    }
    // 清理标签页缓存
    clearCacheForTab(tabId) {
        this.mediaCache.delete(tabId);
    }
}
