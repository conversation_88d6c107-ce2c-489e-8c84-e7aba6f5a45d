import { PipelineNode } from '../PipelineNode';
import { IAudioSegment, IRecognitionResult } from '../../types/media';

/**
 * 音频捕获节点
 */
export class AudioCaptureNode extends PipelineNode<MediaStream, IAudioSegment[]> {
  private audioContext: AudioContext | null = null;
  private analyser: AnalyserNode | null = null;
  private processor: AudioWorkletNode | ScriptProcessorNode | null = null;
  private segments: IAudioSegment[] = [];
  private isRecording = false;
  private segmentDuration = 5000; // 5秒分段

  async process(audioStream: MediaStream): Promise<IAudioSegment[]> {
    try {
      await this.initializeAudioProcessing(audioStream);
      return this.segments;
    } catch (error) {
      this.handleError(error as Error);
      return [];
    }
  }

  private async initializeAudioProcessing(audioStream: MediaStream): Promise<void> {
    this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    
    const source = this.audioContext.createMediaStreamSource(audioStream);
    this.analyser = this.audioContext.createAnalyser();
    
    // 配置分析器
    this.analyser.fftSize = 2048;
    this.analyser.smoothingTimeConstant = 0.8;
    
    // 创建处理器节点 (使用ScriptProcessorNode作为备用方案)
    this.processor = this.audioContext.createScriptProcessor(4096, 1, 1);

    // 连接音频节点
    source.connect(this.analyser);
    this.analyser.connect(this.processor);
    this.processor.connect(this.audioContext.destination);

    // 设置音频处理回调
    (this.processor as ScriptProcessorNode).onaudioprocess = (event) => {
      if (this.isRecording) {
        this.processAudioBuffer(event.inputBuffer);
      }
    };
    
    this.startRecording();
  }

  private startRecording(): void {
    this.isRecording = true;
    this.segments = [];
    
    // 定期创建音频段
    setInterval(() => {
      if (this.isRecording) {
        this.finalizeCurrentSegment();
      }
    }, this.segmentDuration);
  }

  private processAudioBuffer(buffer: AudioBuffer): void {
    const channelData = buffer.getChannelData(0);
    const arrayBuffer = this.float32ToArrayBuffer(channelData);
    
    // 检查音频活动
    if (this.hasAudioActivity(channelData)) {
      this.addToCurrentSegment(arrayBuffer, buffer.sampleRate);
    }
  }

  private float32ToArrayBuffer(float32Array: Float32Array): ArrayBuffer {
    const buffer = new ArrayBuffer(float32Array.length * 2);
    const view = new DataView(buffer);
    
    for (let i = 0; i < float32Array.length; i++) {
      const sample = Math.max(-1, Math.min(1, float32Array[i]));
      view.setInt16(i * 2, sample * 0x7FFF, true);
    }
    
    return buffer;
  }

  private hasAudioActivity(channelData: Float32Array): boolean {
    let sum = 0;
    for (let i = 0; i < channelData.length; i++) {
      sum += Math.abs(channelData[i]);
    }
    const average = sum / channelData.length;
    return average > 0.01; // 音频活动阈值
  }

  private currentSegmentBuffer: ArrayBuffer[] = [];
  private currentSegmentStartTime = Date.now();

  private addToCurrentSegment(buffer: ArrayBuffer, sampleRate: number): void {
    this.currentSegmentBuffer.push(buffer);
  }

  private finalizeCurrentSegment(): void {
    if (this.currentSegmentBuffer.length === 0) return;
    
    const combinedBuffer = this.combineBuffers(this.currentSegmentBuffer);
    const endTime = Date.now();
    
    const segment: IAudioSegment = {
      buffer: combinedBuffer,
      startTime: this.currentSegmentStartTime,
      endTime: endTime,
      sampleRate: this.audioContext?.sampleRate || 44100,
      channels: 1
    };
    
    this.segments.push(segment);
    
    // 重置当前段
    this.currentSegmentBuffer = [];
    this.currentSegmentStartTime = endTime;
    
    // 保持最近的段数量限制
    if (this.segments.length > 10) {
      this.segments.shift();
    }
  }

  private combineBuffers(buffers: ArrayBuffer[]): ArrayBuffer {
    const totalLength = buffers.reduce((sum, buffer) => sum + buffer.byteLength, 0);
    const combined = new ArrayBuffer(totalLength);
    const view = new Uint8Array(combined);
    
    let offset = 0;
    for (const buffer of buffers) {
      view.set(new Uint8Array(buffer), offset);
      offset += buffer.byteLength;
    }
    
    return combined;
  }

  async cleanup(): Promise<void> {
    this.isRecording = false;
    
    if (this.processor) {
      this.processor.disconnect();
      this.processor = null;
    }
    
    if (this.analyser) {
      this.analyser.disconnect();
      this.analyser = null;
    }
    
    if (this.audioContext) {
      await this.audioContext.close();
      this.audioContext = null;
    }
  }
}

/**
 * 语音识别节点
 */
export class SpeechRecognitionNode extends PipelineNode<IAudioSegment, IRecognitionResult | null> {
  private recognitionService: ISpeechRecognitionService;

  constructor(recognitionService: ISpeechRecognitionService) {
    super();
    this.recognitionService = recognitionService;
  }

  async process(audioSegment: IAudioSegment): Promise<IRecognitionResult | null> {
    try {
      const result = await this.recognitionService.recognize(audioSegment);
      return result;
    } catch (error) {
      console.warn('Speech recognition failed:', error);
      return null;
    }
  }
}

/**
 * 语音识别服务接口
 */
export interface ISpeechRecognitionService {
  recognize(audioSegment: IAudioSegment): Promise<IRecognitionResult>;
  getSupportedLanguages(): string[];
  setLanguage(language: string): void;
}

/**
 * Web Speech API 识别服务
 */
export class WebSpeechRecognitionService implements ISpeechRecognitionService {
  private language = 'en-US';
  private recognition: any = null;

  constructor() {
    if ('webkitSpeechRecognition' in window) {
      this.recognition = new (window as any).webkitSpeechRecognition();
      this.setupRecognition();
    } else if ('SpeechRecognition' in window) {
      this.recognition = new (window as any).SpeechRecognition();
      this.setupRecognition();
    }
  }

  private setupRecognition(): void {
    if (!this.recognition) return;
    
    this.recognition.continuous = false;
    this.recognition.interimResults = false;
    this.recognition.maxAlternatives = 1;
    this.recognition.lang = this.language;
  }

  async recognize(audioSegment: IAudioSegment): Promise<IRecognitionResult> {
    return new Promise((resolve, reject) => {
      if (!this.recognition) {
        reject(new Error('Speech recognition not supported'));
        return;
      }

      // 创建音频blob
      const audioBlob = new Blob([audioSegment.buffer], { type: 'audio/wav' });
      const audioUrl = URL.createObjectURL(audioBlob);
      
      // 创建音频元素
      const audio = new Audio(audioUrl);
      
      this.recognition.onresult = (event: any) => {
        const result = event.results[0];
        if (result) {
          resolve({
            text: result[0].transcript,
            confidence: result[0].confidence || 0.8,
            language: this.language,
            startTime: audioSegment.startTime,
            endTime: audioSegment.endTime
          });
        } else {
          reject(new Error('No recognition result'));
        }
        URL.revokeObjectURL(audioUrl);
      };

      this.recognition.onerror = (event: any) => {
        reject(new Error(`Recognition error: ${event.error}`));
        URL.revokeObjectURL(audioUrl);
      };

      // 播放音频并开始识别
      audio.play().then(() => {
        this.recognition!.start();
      }).catch(reject);
    });
  }

  getSupportedLanguages(): string[] {
    return [
      'en-US', 'zh-CN', 'ja-JP', 'ko-KR', 'es-ES', 'fr-FR', 
      'de-DE', 'it-IT', 'pt-BR', 'ru-RU', 'ar-SA'
    ];
  }

  setLanguage(language: string): void {
    this.language = language;
    if (this.recognition) {
      this.recognition.lang = language;
    }
  }
}
