import { PipelineNode } from '../PipelineNode';
import { ISubtitleTrack } from '../../types/media';

/**
 * 字幕提取节点
 */
export class SubtitleExtractionNode extends PipelineNode<ISubtitleTrack, ISubtitleEntry[]> {
  async process(subtitleTrack: ISubtitleTrack): Promise<ISubtitleEntry[]> {
    try {
      let content = subtitleTrack.content;
      
      // 如果没有内容，尝试从URL获取
      if (!content && subtitleTrack.url) {
        content = await this.fetchSubtitleContent(subtitleTrack.url);
      }
      
      if (!content) {
        throw new Error('No subtitle content available');
      }
      
      return this.parseSubtitleContent(content, subtitleTrack.format);
    } catch (error) {
      this.handleError(error as Error);
      return [];
    }
  }

  private async fetchSubtitleContent(url: string): Promise<string> {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch subtitle: ${response.status}`);
    }
    return response.text();
  }

  private parseSubtitleContent(content: string, format: string): ISubtitleEntry[] {
    switch (format.toLowerCase()) {
      case 'srt':
        return this.parseSRT(content);
      case 'vtt':
        return this.parseVTT(content);
      case 'ass':
        return this.parseASS(content);
      case 'json':
        return this.parseJSON(content);
      default:
        throw new Error(`Unsupported subtitle format: ${format}`);
    }
  }

  private parseSRT(content: string): ISubtitleEntry[] {
    const entries: ISubtitleEntry[] = [];
    const blocks = content.trim().split(/\n\s*\n/);
    
    for (const block of blocks) {
      const lines = block.trim().split('\n');
      if (lines.length < 3) continue;
      
      const index = parseInt(lines[0]);
      const timeMatch = lines[1].match(/(\d{2}):(\d{2}):(\d{2}),(\d{3}) --> (\d{2}):(\d{2}):(\d{2}),(\d{3})/);
      
      if (!timeMatch) continue;
      
      const startTime = this.parseTime(timeMatch[1], timeMatch[2], timeMatch[3], timeMatch[4]);
      const endTime = this.parseTime(timeMatch[5], timeMatch[6], timeMatch[7], timeMatch[8]);
      const text = lines.slice(2).join('\n').replace(/<[^>]*>/g, ''); // 移除HTML标签
      
      entries.push({
        index,
        startTime,
        endTime,
        text: text.trim(),
        duration: endTime - startTime
      });
    }
    
    return entries;
  }

  private parseVTT(content: string): ISubtitleEntry[] {
    const entries: ISubtitleEntry[] = [];
    const lines = content.split('\n');
    let currentEntry: Partial<ISubtitleEntry> = {};
    let index = 0;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      if (line.includes('-->')) {
        const timeMatch = line.match(/(\d{2}):(\d{2}):(\d{2})\.(\d{3}) --> (\d{2}):(\d{2}):(\d{2})\.(\d{3})/);
        if (timeMatch) {
          currentEntry.index = ++index;
          currentEntry.startTime = this.parseTime(timeMatch[1], timeMatch[2], timeMatch[3], timeMatch[4]);
          currentEntry.endTime = this.parseTime(timeMatch[5], timeMatch[6], timeMatch[7], timeMatch[8]);
          currentEntry.duration = currentEntry.endTime - currentEntry.startTime;
        }
      } else if (line && currentEntry.startTime !== undefined) {
        currentEntry.text = (currentEntry.text || '') + line + ' ';
      } else if (!line && currentEntry.text) {
        entries.push(currentEntry as ISubtitleEntry);
        currentEntry = {};
      }
    }
    
    // 添加最后一个条目
    if (currentEntry.text) {
      entries.push(currentEntry as ISubtitleEntry);
    }
    
    return entries;
  }

  private parseASS(content: string): ISubtitleEntry[] {
    const entries: ISubtitleEntry[] = [];
    const lines = content.split('\n');
    let index = 0;
    
    for (const line of lines) {
      if (line.startsWith('Dialogue:')) {
        const parts = line.split(',');
        if (parts.length >= 10) {
          const startTime = this.parseASSTime(parts[1]);
          const endTime = this.parseASSTime(parts[2]);
          const text = parts.slice(9).join(',').replace(/\{[^}]*\}/g, ''); // 移除ASS标签
          
          entries.push({
            index: ++index,
            startTime,
            endTime,
            text: text.trim(),
            duration: endTime - startTime
          });
        }
      }
    }
    
    return entries;
  }

  private parseJSON(content: string): ISubtitleEntry[] {
    try {
      const data = JSON.parse(content);
      
      // YouTube JSON格式
      if (data.events) {
        return this.parseYouTubeJSON(data);
      }
      
      // 通用JSON格式
      if (Array.isArray(data)) {
        return data.map((item, index) => ({
          index: index + 1,
          startTime: item.start || item.startTime || 0,
          endTime: item.end || item.endTime || 0,
          text: item.text || item.content || '',
          duration: (item.end || item.endTime || 0) - (item.start || item.startTime || 0)
        }));
      }
      
      throw new Error('Unsupported JSON subtitle format');
    } catch (error) {
      throw new Error(`Failed to parse JSON subtitle: ${error}`);
    }
  }

  private parseYouTubeJSON(data: any): ISubtitleEntry[] {
    const entries: ISubtitleEntry[] = [];
    let index = 0;
    
    for (const event of data.events || []) {
      if (event.segs) {
        let text = '';
        for (const seg of event.segs) {
          text += seg.utf8 || '';
        }
        
        if (text.trim()) {
          entries.push({
            index: ++index,
            startTime: (event.tStartMs || 0) / 1000,
            endTime: ((event.tStartMs || 0) + (event.dDurationMs || 0)) / 1000,
            text: text.trim(),
            duration: (event.dDurationMs || 0) / 1000
          });
        }
      }
    }
    
    return entries;
  }

  private parseTime(hours: string, minutes: string, seconds: string, milliseconds: string): number {
    return parseInt(hours) * 3600 + 
           parseInt(minutes) * 60 + 
           parseInt(seconds) + 
           parseInt(milliseconds) / 1000;
  }

  private parseASSTime(timeStr: string): number {
    const match = timeStr.match(/(\d+):(\d{2}):(\d{2})\.(\d{2})/);
    if (!match) return 0;
    
    return parseInt(match[1]) * 3600 + 
           parseInt(match[2]) * 60 + 
           parseInt(match[3]) + 
           parseInt(match[4]) / 100;
  }
}

/**
 * 字幕条目接口
 */
export interface ISubtitleEntry {
  index: number;
  startTime: number;
  endTime: number;
  text: string;
  duration: number;
}

/**
 * 字幕同步节点
 */
export class SubtitleSyncNode extends PipelineNode<ISubtitleEntry[], ISubtitleEntry[]> {
  private videoElement: HTMLVideoElement | null = null;
  private syncOffset = 0; // 同步偏移量（秒）

  constructor(videoElement?: HTMLVideoElement, syncOffset = 0) {
    super();
    this.videoElement = videoElement;
    this.syncOffset = syncOffset;
  }

  async process(entries: ISubtitleEntry[]): Promise<ISubtitleEntry[]> {
    if (!this.videoElement) {
      return entries; // 无法同步，返回原始条目
    }

    const currentTime = this.videoElement.currentTime;
    
    // 应用同步偏移
    const syncedEntries = entries.map(entry => ({
      ...entry,
      startTime: entry.startTime + this.syncOffset,
      endTime: entry.endTime + this.syncOffset
    }));

    // 过滤出当前时间附近的条目（前后30秒）
    const relevantEntries = syncedEntries.filter(entry => 
      entry.endTime >= currentTime - 30 && 
      entry.startTime <= currentTime + 30
    );

    return relevantEntries;
  }

  setSyncOffset(offset: number): void {
    this.syncOffset = offset;
  }

  setVideoElement(videoElement: HTMLVideoElement): void {
    this.videoElement = videoElement;
  }
}

/**
 * 字幕过滤节点
 */
export class SubtitleFilterNode extends PipelineNode<ISubtitleEntry[], ISubtitleEntry[]> {
  private filters: SubtitleFilter[] = [];

  constructor(filters: SubtitleFilter[] = []) {
    super();
    this.filters = filters;
  }

  async process(entries: ISubtitleEntry[]): Promise<ISubtitleEntry[]> {
    let filteredEntries = entries;

    for (const filter of this.filters) {
      filteredEntries = filteredEntries.filter(entry => filter.test(entry));
    }

    return filteredEntries;
  }

  addFilter(filter: SubtitleFilter): void {
    this.filters.push(filter);
  }

  removeFilter(filterName: string): void {
    this.filters = this.filters.filter(f => f.name !== filterName);
  }
}

/**
 * 字幕过滤器接口
 */
export interface SubtitleFilter {
  name: string;
  test(entry: ISubtitleEntry): boolean;
}

/**
 * 预定义过滤器
 */
export const SubtitleFilters = {
  // 过滤空白字幕
  nonEmpty: {
    name: 'non-empty',
    test: (entry: ISubtitleEntry) => entry.text.trim().length > 0
  },
  
  // 过滤最小持续时间
  minDuration: (seconds: number) => ({
    name: 'min-duration',
    test: (entry: ISubtitleEntry) => entry.duration >= seconds
  }),
  
  // 过滤音乐符号
  noMusicSymbols: {
    name: 'no-music',
    test: (entry: ISubtitleEntry) => !/[♪♫♬♩]/.test(entry.text)
  },
  
  // 过滤说话人标记
  noSpeakerLabels: {
    name: 'no-speaker-labels',
    test: (entry: ISubtitleEntry) => !/^[A-Z\s]+:/.test(entry.text)
  }
};
