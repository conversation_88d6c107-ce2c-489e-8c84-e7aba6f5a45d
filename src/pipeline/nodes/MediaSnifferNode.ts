import { PipelineNode } from '../PipelineNode';
import { IMediaInfo } from '../../types/media';

/**
 * 媒体嗅探节点
 */
export class MediaSnifferNode extends PipelineNode<chrome.webRequest.WebRequestDetails, IMediaInfo | null> {
  private mediaCache = new Map<number, IMediaInfo[]>();
  
  async process(request: chrome.webRequest.WebRequestDetails): Promise<IMediaInfo | null> {
    // 只处理媒体类型请求
    if (!this.isMediaRequest(request)) {
      return null;
    }
    
    const mediaInfo = this.createMediaInfo(request);
    this.cacheMediaInfo(request.tabId, mediaInfo);
    return mediaInfo;
  }
  
  private isMediaRequest(request: chrome.webRequest.WebRequestDetails): boolean {
    const contentType = request.responseHeaders?.find(h => h.name.toLowerCase() === 'content-type')?.value;
    return contentType?.includes('video/') || contentType?.includes('audio/') || false;
  }
  
  private createMediaInfo(request: chrome.webRequest.WebRequestDetails): IMediaInfo {
    return {
      url: request.url,
      type: this.getMediaType(request),
      initiator: request.initiator || '',
      requestHeaders: this.serializeHeaders(request.responseHeaders),
      tabId: request.tabId,
      cookies: '' // 需要额外获取
    };
  }
  
  private getMediaType(request: chrome.webRequest.WebRequestDetails): IMediaInfo['type'] {
    if (request.url.endsWith('.m3u8')) return 'm3u8';
    if (request.url.includes('.mp4') || request.url.includes('.mov')) return 'video';
    return 'audio';
  }
  
  private serializeHeaders(headers?: chrome.webRequest.HttpHeader[]): Record<string, string> {
    if (!headers) return {};
    return headers.reduce((acc, header) => {
      acc[header.name] = header.value || '';
      return acc;
    }, {} as Record<string, string>);
  }
  
  private cacheMediaInfo(tabId: number, mediaInfo: IMediaInfo) {
    if (!this.mediaCache.has(tabId)) {
      this.mediaCache.set(tabId, []);
    }
    this.mediaCache.get(tabId)?.push(mediaInfo);
  }
  
  // 获取标签页的媒体信息
  getMediaForTab(tabId: number): IMediaInfo | null {
    const mediaList = this.mediaCache.get(tabId);
    return mediaList?.length ? mediaList[0] : null;
  }
  
  // 清理标签页缓存
  clearCacheForTab(tabId: number) {
    this.mediaCache.delete(tabId);
  }
}