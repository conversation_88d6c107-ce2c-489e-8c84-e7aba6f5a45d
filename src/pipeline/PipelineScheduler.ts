import { PipelineNode } from './PipelineNode';

/**
 * 流水线调度器
 */
export class PipelineScheduler {
  private pipelines: Map<string, PipelineNode<any, any>[]> = new Map();
  private activeTasks: Set<Promise<any>> = new Set();

  /**
   * 注册流水线
   */
  registerPipeline(
    name: string,
    nodes: PipelineNode<any, any>[],
    concurrency = 3
  ) {
    this.pipelines.set(name, [
      ...nodes,
      new ParallelLimitNode(concurrency), // 添加并行控制节点
    ]);
  }

  /**
   * 执行流水线任务
   */
  async execute<T>(pipelineName: string, input: any): Promise<T> {
    const pipeline = this.pipelines.get(pipelineName);
    if (!pipeline) {
      throw new Error(`Pipeline ${pipelineName} not found`);
    }

    let result = input;
    for (const node of pipeline) {
      try {
        const task = node.process(result);
        this.activeTasks.add(task);
        result = await task;
        this.activeTasks.delete(task);
      } catch (error) {
        this.handlePipelineError(pipelineName, error);
        throw error;
      }
    }
    return result;
  }

  private handlePipelineError(pipelineName: string, error: Error) {
    console.error(`[${pipelineName}] Pipeline error:`, error);
    // TODO: 实现重试逻辑
  }
}

/**
 * 并行控制节点
 */
class ParallelLimitNode extends PipelineNode<any, any> {
  constructor(private concurrency: number) {
    super();
  }

  async process(inputs: any[]) {
    const results: any[] = [];
    const queue = [...inputs];
    
    while (queue.length) {
      const batch = queue.splice(0, this.concurrency);
      const batchResults = await Promise.all(batch);
      results.push(...batchResults);
    }
    
    return results;
  }
}