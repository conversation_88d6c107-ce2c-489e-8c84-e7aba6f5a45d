/**
 * 媒体信息接口
 */
export interface IMediaInfo {
  url: string;          // 媒体文件的真实URL
  type: 'video' | 'audio' | 'm3u8';
  initiator: string;    // 发起请求的网页URL
  requestHeaders?: Record<string, string>;
  cookies?: string;      // 对应域名的Cookie
  tabId: number;        // 关联标签页ID
}

/**
 * 内容类型枚举
 */
export enum ContentType {
  SUBTITLE = 'subtitle',    // 有字幕内容
  AUDIO_ONLY = 'audio_only' // 纯音频内容
}

/**
 * 平台类型枚举
 */
export enum PlatformType {
  YOUTUBE = 'youtube',
  TIKTOK = 'tiktok',
  BILIBILI = 'bilibili',
  NETFLIX = 'netflix',
  GENERIC = 'generic'
}

/**
 * 内容信息接口
 */
export interface IContentInfo {
  platform: PlatformType;
  contentType: ContentType;
  mediaInfo: IMediaInfo;
  subtitles?: ISubtitleTrack[];
  audioBuffer?: ArrayBuffer;
  metadata?: Record<string, any>;
}

/**
 * 字幕轨道接口
 */
export interface ISubtitleTrack {
  language: string;
  url?: string;
  content?: string;
  format: 'srt' | 'vtt' | 'ass' | 'json';
  isAutoGenerated: boolean;
}

/**
 * 音频片段接口
 */
export interface IAudioSegment {
  buffer: ArrayBuffer;
  startTime: number;
  endTime: number;
  sampleRate: number;
  channels: number;
}

/**
 * 识别结果接口
 */
export interface IRecognitionResult {
  text: string;
  confidence: number;
  language: string;
  startTime: number;
  endTime: number;
}

/**
 * 翻译结果接口
 */
export interface ITranslationResult {
  originalText: string;
  translatedText: string;
  sourceLanguage: string;
  targetLanguage: string;
  confidence: number;
  provider: string;
}

/**
 * TTS结果接口
 */
export interface ITTSResult {
  audioBuffer: ArrayBuffer;
  duration: number;
  format: string;
  provider: string;
}