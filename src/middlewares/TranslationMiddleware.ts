import { BaseMiddleware, IMiddlewareContext } from '../core/MiddlewareEngine';
import { TranslationEngineManager } from '../services/TranslationEngine';

/**
 * 翻译中间件
 */
export class TranslationMiddleware extends BaseMiddleware {
  public name = 'translation';
  public priority = 30;

  private translationEngine: TranslationEngineManager;
  private cache: Map<string, any> = new Map();
  private cacheMaxSize = 1000;

  constructor(translationEngine: TranslationEngineManager) {
    super();
    this.translationEngine = translationEngine;
  }

  async process(context: IMiddlewareContext, next: () => Promise<void>): Promise<void> {
    if (this.shouldSkip(context)) return;

    const { data } = context;
    
    // 检查是否有需要翻译的文本
    if (!data.text || !data.targetLanguage) {
      await next();
      return;
    }

    const startTime = Date.now();
    
    try {
      // 检查缓存
      const cacheKey = this.generateCacheKey(data.text, data.targetLanguage, data.sourceLanguage);
      let translationResult = this.cache.get(cacheKey);

      if (!translationResult) {
        // 执行翻译
        translationResult = await this.translationEngine.translate(
          data.text,
          data.targetLanguage,
          data.sourceLanguage
        );

        // 缓存结果
        this.setCacheItem(cacheKey, translationResult);
      }

      // 更新上下文数据
      context.data = {
        ...data,
        translationResult,
        translatedText: translationResult.translatedText,
        processingLatency: Date.now() - startTime
      };

      // 设置中间件状态
      this.setState(context, 'translationTime', Date.now() - startTime);
      this.setState(context, 'cacheHit', this.cache.has(cacheKey));

      await next();
    } catch (error) {
      console.error('[TranslationMiddleware] Translation failed:', error);
      
      // 设置错误状态但继续处理
      context.data = {
        ...data,
        translationError: error,
        translatedText: data.text, // 使用原文作为备用
        processingLatency: Date.now() - startTime
      };

      await next();
    }
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(text: string, targetLang: string, sourceLang?: string): string {
    const source = sourceLang || 'auto';
    return `${source}->${targetLang}:${this.hashText(text)}`;
  }

  /**
   * 文本哈希
   */
  private hashText(text: string): string {
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString(36);
  }

  /**
   * 设置缓存项
   */
  private setCacheItem(key: string, value: any): void {
    if (this.cache.size >= this.cacheMaxSize) {
      // 删除最旧的缓存项
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }

  async cleanup(): Promise<void> {
    this.cache.clear();
    await super.cleanup();
  }
}

/**
 * TTS中间件
 */
export class TTSMiddleware extends BaseMiddleware {
  public name = 'tts';
  public priority = 40;

  private ttsEngine: any; // TTSEngineManager
  private audioCache: Map<string, ArrayBuffer> = new Map();
  private cacheMaxSize = 500;

  constructor(ttsEngine: any) {
    super();
    this.ttsEngine = ttsEngine;
  }

  async process(context: IMiddlewareContext, next: () => Promise<void>): Promise<void> {
    if (this.shouldSkip(context)) return;

    const { data } = context;
    
    // 检查是否有需要合成的文本
    if (!data.translatedText || !data.targetLanguage) {
      await next();
      return;
    }

    const startTime = Date.now();
    
    try {
      // 检查音频缓存
      const cacheKey = this.generateAudioCacheKey(data.translatedText, data.targetLanguage);
      let audioBuffer = this.audioCache.get(cacheKey);

      if (!audioBuffer) {
        // 执行TTS合成
        const ttsResult = await this.ttsEngine.synthesize(
          data.translatedText,
          data.targetLanguage
        );

        audioBuffer = ttsResult.audioBuffer;
        
        // 缓存音频
        this.setAudioCacheItem(cacheKey, audioBuffer);
      }

      // 更新上下文数据
      context.data = {
        ...data,
        audioBuffer,
        ttsLatency: Date.now() - startTime
      };

      // 设置中间件状态
      this.setState(context, 'ttsTime', Date.now() - startTime);
      this.setState(context, 'audioCacheHit', this.audioCache.has(cacheKey));

      await next();
    } catch (error) {
      console.error('[TTSMiddleware] TTS synthesis failed:', error);
      
      // 设置错误状态
      context.data = {
        ...data,
        ttsError: error,
        ttsLatency: Date.now() - startTime
      };

      await next();
    }
  }

  /**
   * 生成音频缓存键
   */
  private generateAudioCacheKey(text: string, language: string): string {
    return `${language}:${this.hashText(text)}`;
  }

  /**
   * 文本哈希
   */
  private hashText(text: string): string {
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return hash.toString(36);
  }

  /**
   * 设置音频缓存项
   */
  private setAudioCacheItem(key: string, audioBuffer: ArrayBuffer): void {
    if (this.audioCache.size >= this.cacheMaxSize) {
      // 删除最旧的缓存项
      const firstKey = this.audioCache.keys().next().value;
      this.audioCache.delete(firstKey);
    }
    this.audioCache.set(key, audioBuffer);
  }

  async cleanup(): Promise<void> {
    this.audioCache.clear();
    await super.cleanup();
  }
}

/**
 * 同步中间件
 */
export class SyncMiddleware extends BaseMiddleware {
  public name = 'sync';
  public priority = 50;

  private syncEngine: any; // RealtimeSyncEngine

  constructor(syncEngine: any) {
    super();
    this.syncEngine = syncEngine;
  }

  async process(context: IMiddlewareContext, next: () => Promise<void>): Promise<void> {
    if (this.shouldSkip(context)) return;

    const { data } = context;
    
    // 检查是否有音频数据和时间信息
    if (!data.audioBuffer || typeof data.startTime !== 'number') {
      await next();
      return;
    }

    try {
      // 创建同步音频项
      const syncAudioItem = {
        id: this.generateAudioId(),
        audioBuffer: data.audioBuffer,
        startTime: data.startTime,
        endTime: data.endTime || data.startTime + (data.duration || 3),
        duration: data.duration || 3,
        priority: data.priority || 1,
        metadata: {
          originalText: data.text || '',
          translatedText: data.translatedText || '',
          language: data.targetLanguage || 'unknown',
          confidence: data.confidence || 1,
          processingLatency: (data.processingLatency || 0) + (data.ttsLatency || 0)
        }
      };

      // 添加到同步引擎
      this.syncEngine.addAudioItem(syncAudioItem);

      // 更新上下文数据
      context.data = {
        ...data,
        syncAudioItem,
        syncTime: Date.now()
      };

      // 设置中间件状态
      this.setState(context, 'audioItemId', syncAudioItem.id);
      this.setState(context, 'syncCompleted', true);

      await next();
    } catch (error) {
      console.error('[SyncMiddleware] Sync failed:', error);
      
      context.data = {
        ...data,
        syncError: error
      };

      await next();
    }
  }

  /**
   * 生成音频ID
   */
  private generateAudioId(): string {
    return `audio_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 性能监控中间件
 */
export class PerformanceMiddleware extends BaseMiddleware {
  public name = 'performance';
  public priority = 1; // 最高优先级，最先执行

  private metrics: Map<string, any> = new Map();

  async process(context: IMiddlewareContext, next: () => Promise<void>): Promise<void> {
    const startTime = Date.now();
    const processingId = context.metadata.processingId;

    // 记录开始时间
    this.setState(context, 'startTime', startTime);

    try {
      await next();
      
      // 记录成功指标
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      
      this.updateMetrics(processingId, {
        totalTime,
        success: true,
        timestamp: endTime
      });

      context.data = {
        ...context.data,
        performanceMetrics: {
          totalProcessingTime: totalTime,
          success: true
        }
      };

    } catch (error) {
      // 记录错误指标
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      
      this.updateMetrics(processingId, {
        totalTime,
        success: false,
        error: error.message,
        timestamp: endTime
      });

      throw error;
    }
  }

  /**
   * 更新性能指标
   */
  private updateMetrics(processingId: string, metrics: any): void {
    this.metrics.set(processingId, metrics);
    
    // 保持最近1000条记录
    if (this.metrics.size > 1000) {
      const firstKey = this.metrics.keys().next().value;
      this.metrics.delete(firstKey);
    }
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats(): {
    totalProcessed: number;
    averageTime: number;
    successRate: number;
    recentErrors: string[];
  } {
    const values = Array.from(this.metrics.values());
    const totalProcessed = values.length;
    const successful = values.filter(v => v.success);
    const failed = values.filter(v => !v.success);
    
    const averageTime = successful.length > 0 ? 
      successful.reduce((sum, v) => sum + v.totalTime, 0) / successful.length : 0;
    
    const successRate = totalProcessed > 0 ? successful.length / totalProcessed : 0;
    
    const recentErrors = failed
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, 10)
      .map(v => v.error);

    return {
      totalProcessed,
      averageTime,
      successRate,
      recentErrors
    };
  }

  async cleanup(): Promise<void> {
    this.metrics.clear();
    await super.cleanup();
  }
}
