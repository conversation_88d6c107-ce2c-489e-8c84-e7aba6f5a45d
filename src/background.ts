/**
 * ChronoTranslate 背景脚本
 */

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener((details) => {
  console.log('ChronoTranslate installed:', details.reason);
  
  if (details.reason === 'install') {
    // 首次安装时设置默认配置
    chrome.storage.sync.set({
      chronotranslate_config: {
        primaryEngine: 'google',
        fallbackEngines: ['openai', 'browser'],
        targetLanguage: 'zh-CN',
        autoStart: false,
        showSubtitles: true,
        subtitlePosition: 'bottom'
      }
    });
    
    // 打开欢迎页面
    chrome.tabs.create({
      url: chrome.runtime.getURL('welcome/welcome.html')
    });
  }
});

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  handleMessage(message, sender).then(sendResponse).catch(error => {
    console.error('Background message handling failed:', error);
    sendResponse({ success: false, error: error.message });
  });
  return true; // 异步响应
});

// 处理消息
async function handleMessage(message: any, sender: chrome.runtime.MessageSender): Promise<any> {
  switch (message.action) {
    case 'GET_CONFIG':
      return getStoredConfig();
      
    case 'SET_CONFIG':
      return setStoredConfig(message.config);
      
    case 'GET_TAB_INFO':
      return getTabInfo(sender.tab?.id);
      
    case 'LOG_EVENT':
      logEvent(message.event, message.data);
      return { success: true };
      
    default:
      throw new Error(`Unknown action: ${message.action}`);
  }
}

// 获取存储的配置
async function getStoredConfig(): Promise<any> {
  return new Promise((resolve) => {
    chrome.storage.sync.get(['chronotranslate_config'], (result) => {
      resolve(result.chronotranslate_config || {});
    });
  });
}

// 设置存储的配置
async function setStoredConfig(config: any): Promise<void> {
  return new Promise((resolve) => {
    chrome.storage.sync.set({ chronotranslate_config: config }, () => {
      resolve();
    });
  });
}

// 获取标签页信息
async function getTabInfo(tabId?: number): Promise<any> {
  if (!tabId) return null;
  
  return new Promise((resolve) => {
    chrome.tabs.get(tabId, (tab) => {
      if (chrome.runtime.lastError) {
        resolve(null);
      } else {
        resolve({
          id: tab.id,
          url: tab.url,
          title: tab.title,
          active: tab.active
        });
      }
    });
  });
}

// 记录事件（用于分析和调试）
function logEvent(event: string, data?: any): void {
  const logEntry = {
    timestamp: Date.now(),
    event,
    data,
    url: data?.url || 'unknown'
  };
  
  console.log('ChronoTranslate Event:', logEntry);
  
  // 可以在这里添加更多的日志处理逻辑
  // 比如发送到分析服务或存储到本地
}

// 监听标签页更新
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    // 检查是否为支持的平台
    const supportedPlatforms = [
      'youtube.com',
      'youtu.be',
      'tiktok.com',
      'bilibili.com',
      'netflix.com'
    ];
    
    const isSupported = supportedPlatforms.some(platform => 
      tab.url!.includes(platform)
    );
    
    if (isSupported) {
      // 向content script发送平台检测消息
      chrome.tabs.sendMessage(tabId, {
        action: 'PLATFORM_DETECTED',
        url: tab.url,
        platform: detectPlatform(tab.url)
      }).catch(() => {
        // 忽略错误，可能content script还未加载
      });
    }
  }
});

// 检测平台类型
function detectPlatform(url: string): string {
  if (url.includes('youtube.com') || url.includes('youtu.be')) {
    return 'youtube';
  } else if (url.includes('tiktok.com')) {
    return 'tiktok';
  } else if (url.includes('bilibili.com')) {
    return 'bilibili';
  } else if (url.includes('netflix.com')) {
    return 'netflix';
  }
  return 'unknown';
}

// 监听网络请求（用于媒体嗅探）
chrome.webRequest.onResponseStarted.addListener(
  (details) => {
    // 检查是否为媒体请求
    const contentType = details.responseHeaders?.find(
      header => header.name.toLowerCase() === 'content-type'
    )?.value;
    
    if (contentType && (
      contentType.includes('video/') || 
      contentType.includes('audio/') ||
      details.url.includes('.m3u8')
    )) {
      // 向对应标签页发送媒体信息
      chrome.tabs.sendMessage(details.tabId, {
        action: 'MEDIA_DETECTED',
        mediaInfo: {
          url: details.url,
          type: getMediaType(details.url, contentType),
          initiator: details.initiator,
          tabId: details.tabId,
          requestHeaders: details.responseHeaders
        }
      }).catch(() => {
        // 忽略错误
      });
    }
  },
  {
    urls: ["<all_urls>"],
    types: ["xmlhttprequest", "other"]
  },
  ["responseHeaders"]
);

// 获取媒体类型
function getMediaType(url: string, contentType: string): string {
  if (url.includes('.m3u8')) return 'm3u8';
  if (contentType.includes('video/')) return 'video';
  if (contentType.includes('audio/')) return 'audio';
  return 'unknown';
}

// 处理扩展图标点击
chrome.action.onClicked.addListener((tab) => {
  // 打开popup或执行默认操作
  if (tab.id) {
    chrome.tabs.sendMessage(tab.id, {
      action: 'TOGGLE_TRANSLATION'
    }).catch(() => {
      // 如果content script未加载，注入脚本
      chrome.scripting.executeScript({
        target: { tabId: tab.id! },
        files: ['dist/main.js']
      });
    });
  }
});

// 监听存储变化
chrome.storage.onChanged.addListener((changes, namespace) => {
  if (namespace === 'sync' && changes.chronotranslate_config) {
    // 配置发生变化，通知所有标签页
    chrome.tabs.query({}, (tabs) => {
      tabs.forEach(tab => {
        if (tab.id) {
          chrome.tabs.sendMessage(tab.id, {
            action: 'CONFIG_CHANGED',
            config: changes.chronotranslate_config.newValue
          }).catch(() => {
            // 忽略错误
          });
        }
      });
    });
  }
});

// 定期清理缓存和日志
setInterval(() => {
  // 这里可以添加清理逻辑
  console.log('ChronoTranslate background cleanup');
}, 30 * 60 * 1000); // 每30分钟执行一次

// 导出类型定义（如果需要）
export interface BackgroundMessage {
  action: string;
  [key: string]: any;
}

export interface MediaInfo {
  url: string;
  type: string;
  initiator?: string;
  tabId: number;
  requestHeaders?: chrome.webRequest.HttpHeader[];
}
