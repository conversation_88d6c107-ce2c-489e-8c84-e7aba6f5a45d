import { TranslationController } from './controllers/TranslationController';
import { PlatformAdapterFactory } from './adapters/PlatformAdapter';
import { YouTubeAdapter } from './adapters/YouTubeAdapter';
import { GoogleTranslateEngine, OpenAITranslateEngine, BrowserTranslateEngine } from './services/TranslationEngine';
import { GoogleTTSEngine, AzureTTSEngine, BrowserTTSEngine } from './services/TTSEngine';

/**
 * ChronoTranslate 主入口
 */
class ChronoTranslate {
  private controller: TranslationController;
  private isInitialized = false;

  constructor() {
    this.controller = new TranslationController();
  }

  /**
   * 初始化应用
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // 注册平台适配器
      this.registerPlatformAdapters();

      // 配置翻译引擎
      await this.setupTranslationEngines();

      // 配置TTS引擎
      await this.setupTTSEngines();

      // 监听页面变化
      this.setupPageChangeListener();

      // 设置消息监听
      this.setupMessageListener();

      this.isInitialized = true;
      console.log('ChronoTranslate initialized successfully');
    } catch (error) {
      console.error('Failed to initialize ChronoTranslate:', error);
      throw error;
    }
  }

  /**
   * 注册平台适配器
   */
  private registerPlatformAdapters(): void {
    // 注册YouTube适配器
    PlatformAdapterFactory.register('youtube', YouTubeAdapter);

    // TODO: 注册其他平台适配器
    // PlatformAdapterFactory.register('tiktok', TikTokAdapter);
    // PlatformAdapterFactory.register('bilibili', BilibiliAdapter);
    
    console.log('Platform adapters registered:', PlatformAdapterFactory.getSupportedPlatforms());
  }

  /**
   * 设置翻译引擎
   */
  private async setupTranslationEngines(): Promise<void> {
    // 从存储中获取API密钥
    const config = await this.getStoredConfig();

    // 注册Google翻译引擎
    if (config.googleApiKey) {
      const googleEngine = new GoogleTranslateEngine(config.googleApiKey);
      this.controller['translationEngine'].registerEngine('google', googleEngine);
    }

    // 注册OpenAI翻译引擎
    if (config.openaiApiKey) {
      const openaiEngine = new OpenAITranslateEngine(config.openaiApiKey);
      this.controller['translationEngine'].registerEngine('openai', openaiEngine);
    }

    // 注册浏览器内置翻译引擎
    const browserEngine = new BrowserTranslateEngine();
    if (await browserEngine.isAvailable()) {
      this.controller['translationEngine'].registerEngine('browser', browserEngine);
    }

    // 设置主引擎和备用引擎
    const primaryEngine = config.primaryEngine || 'google';
    const fallbackEngines = config.fallbackEngines || ['openai', 'browser'];

    try {
      this.controller['translationEngine'].setPrimaryEngine(primaryEngine);
      this.controller['translationEngine'].setFallbackEngines(fallbackEngines);
    } catch (error) {
      console.warn('Failed to set translation engines:', error);
    }
  }

  /**
   * 设置TTS引擎
   */
  private async setupTTSEngines(): Promise<void> {
    // 从存储中获取API密钥
    const config = await this.getStoredConfig();

    // 注册Google TTS引擎
    if (config.googleApiKey) {
      const googleTTSEngine = new GoogleTTSEngine(config.googleApiKey);
      this.controller['ttsEngine'].registerEngine('google', googleTTSEngine);
    }

    // 注册Azure TTS引擎
    if (config.azureApiKey && config.azureRegion) {
      const azureTTSEngine = new AzureTTSEngine(config.azureApiKey, config.azureRegion);
      this.controller['ttsEngine'].registerEngine('azure', azureTTSEngine);
    }

    // 注册浏览器内置TTS引擎
    const browserTTSEngine = new BrowserTTSEngine();
    if (await browserTTSEngine.isAvailable()) {
      this.controller['ttsEngine'].registerEngine('browser', browserTTSEngine);
    }

    // 设置主引擎和备用引擎
    const primaryTTSEngine = config.primaryTTSEngine || 'google';
    const fallbackTTSEngines = config.fallbackTTSEngines || ['azure', 'browser'];

    try {
      this.controller['ttsEngine'].setPrimaryEngine(primaryTTSEngine);
      this.controller['ttsEngine'].setFallbackEngines(fallbackTTSEngines);
    } catch (error) {
      console.warn('Failed to set TTS engines:', error);
    }
  }

  /**
   * 获取存储的配置
   */
  private async getStoredConfig(): Promise<any> {
    return new Promise((resolve) => {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        chrome.storage.sync.get(['chronotranslate_config'], (result) => {
          resolve(result.chronotranslate_config || {});
        });
      } else {
        // 开发环境或非扩展环境
        const config = localStorage.getItem('chronotranslate_config');
        resolve(config ? JSON.parse(config) : {});
      }
    });
  }

  /**
   * 设置页面变化监听
   */
  private setupPageChangeListener(): void {
    // 监听URL变化
    let currentUrl = window.location.href;
    
    const checkUrlChange = () => {
      if (window.location.href !== currentUrl) {
        currentUrl = window.location.href;
        this.handlePageChange(currentUrl);
      }
    };

    // 监听pushState和replaceState
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function(...args) {
      originalPushState.apply(history, args);
      setTimeout(checkUrlChange, 100);
    };

    history.replaceState = function(...args) {
      originalReplaceState.apply(history, args);
      setTimeout(checkUrlChange, 100);
    };

    // 监听popstate事件
    window.addEventListener('popstate', checkUrlChange);

    // 定期检查URL变化（备用方案）
    setInterval(checkUrlChange, 1000);
  }

  /**
   * 处理页面变化
   */
  private async handlePageChange(url: string): Promise<void> {
    console.log('Page changed:', url);

    // 停止当前翻译
    if (this.controller.getStatus().isActive) {
      await this.controller.stopTranslation();
    }

    // 检查新页面是否支持
    const adapter = PlatformAdapterFactory.create(url);
    if (adapter) {
      console.log('Supported platform detected:', adapter.constructor.name);
      
      // 延迟初始化，等待页面加载完成
      setTimeout(async () => {
        try {
          const success = await this.controller.initialize(url);
          if (success) {
            this.showTranslationButton();
          }
        } catch (error) {
          console.error('Failed to initialize controller for new page:', error);
        }
      }, 2000);
    }
  }

  /**
   * 设置消息监听（用于与popup通信）
   */
  private setupMessageListener(): void {
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        this.handleMessage(message).then(sendResponse);
        return true; // 异步响应
      });
    }

    // 监听window消息（用于开发环境）
    window.addEventListener('message', (event) => {
      if (event.source === window && event.data.type === 'CHRONOTRANSLATE_MESSAGE') {
        this.handleMessage(event.data.payload);
      }
    });
  }

  /**
   * 处理消息
   */
  private async handleMessage(message: any): Promise<any> {
    try {
      switch (message.action) {
        case 'START_TRANSLATION':
          await this.startTranslation(message.targetLanguage);
          return { success: true };

        case 'STOP_TRANSLATION':
          await this.stopTranslation();
          return { success: true };

        case 'GET_STATUS':
          return this.controller.getStatus();

        case 'GET_SUPPORTED_PLATFORMS':
          return PlatformAdapterFactory.getSupportedPlatforms();

        default:
          throw new Error(`Unknown action: ${message.action}`);
      }
    } catch (error) {
      console.error('Message handling failed:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 启动翻译
   */
  async startTranslation(targetLanguage: string = 'zh-CN'): Promise<void> {
    try {
      const success = await this.controller.initialize(window.location.href);
      if (!success) {
        throw new Error('Failed to initialize translation controller');
      }

      await this.controller.startTranslation(targetLanguage);
      console.log('Translation started for language:', targetLanguage);
    } catch (error) {
      console.error('Failed to start translation:', error);
      throw error;
    }
  }

  /**
   * 停止翻译
   */
  async stopTranslation(): Promise<void> {
    await this.controller.stopTranslation();
    this.hideTranslationButton();
    console.log('Translation stopped');
  }

  /**
   * 显示翻译按钮
   */
  private showTranslationButton(): void {
    // 移除现有按钮
    const existingButton = document.getElementById('chronotranslate-main-button');
    if (existingButton) {
      existingButton.remove();
    }

    // 创建新按钮
    const button = document.createElement('button');
    button.id = 'chronotranslate-main-button';
    button.textContent = '🌐 翻译';
    button.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 10001;
      background: linear-gradient(45deg, #4CAF50, #45a049);
      color: white;
      border: none;
      border-radius: 25px;
      padding: 10px 20px;
      font-size: 14px;
      font-weight: bold;
      cursor: pointer;
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
      transition: all 0.3s ease;
    `;

    // 添加悬停效果
    button.addEventListener('mouseenter', () => {
      button.style.transform = 'scale(1.05)';
      button.style.boxShadow = '0 6px 12px rgba(0,0,0,0.3)';
    });

    button.addEventListener('mouseleave', () => {
      button.style.transform = 'scale(1)';
      button.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
    });

    // 点击事件
    button.addEventListener('click', async () => {
      const status = this.controller.getStatus();
      if (status.isActive) {
        await this.stopTranslation();
        button.textContent = '🌐 翻译';
        button.style.background = 'linear-gradient(45deg, #4CAF50, #45a049)';
      } else {
        try {
          await this.startTranslation();
          button.textContent = '⏹️ 停止';
          button.style.background = 'linear-gradient(45deg, #f44336, #da190b)';
        } catch (error) {
          alert('翻译启动失败: ' + error.message);
        }
      }
    });

    document.body.appendChild(button);
  }

  /**
   * 隐藏翻译按钮
   */
  private hideTranslationButton(): void {
    const button = document.getElementById('chronotranslate-main-button');
    if (button) {
      button.textContent = '🌐 翻译';
      button.style.background = 'linear-gradient(45deg, #4CAF50, #45a049)';
    }
  }
}

// 全局实例
let chronoTranslate: ChronoTranslate | null = null;

// 初始化应用
async function initializeApp(): Promise<void> {
  if (chronoTranslate) return;

  try {
    chronoTranslate = new ChronoTranslate();
    await chronoTranslate.initialize();
  } catch (error) {
    console.error('Failed to initialize ChronoTranslate:', error);
  }
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeApp);
} else {
  initializeApp();
}

// 导出全局接口
(window as any).ChronoTranslate = {
  start: (targetLanguage?: string) => chronoTranslate?.startTranslation(targetLanguage),
  stop: () => chronoTranslate?.stopTranslation(),
  getStatus: () => chronoTranslate?.controller.getStatus()
};
