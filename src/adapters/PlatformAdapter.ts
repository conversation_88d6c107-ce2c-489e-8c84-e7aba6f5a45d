import { IContentInfo, IMediaInfo, ISubtitleTrack, PlatformType } from '../types/media';

/**
 * 平台适配器基类
 */
export abstract class PlatformAdapter {
  protected platformType: PlatformType;

  constructor(platformType: PlatformType) {
    this.platformType = platformType;
  }

  /**
   * 检测当前页面是否匹配该平台
   */
  abstract matches(url: string): boolean;

  /**
   * 获取页面内容信息
   */
  abstract getContentInfo(): Promise<IContentInfo | null>;

  /**
   * 获取字幕信息
   */
  abstract getSubtitles(): Promise<ISubtitleTrack[]>;

  /**
   * 获取音频流
   */
  abstract getAudioStream(): Promise<MediaStream | null>;

  /**
   * 注入翻译界面
   */
  abstract injectUI(container: HTMLElement): void;

  /**
   * 清理资源
   */
  abstract cleanup(): void;

  /**
   * 获取平台特定的媒体信息
   */
  protected abstract extractMediaInfo(): Promise<IMediaInfo | null>;

  /**
   * 获取视频元素
   */
  protected getVideoElement(): HTMLVideoElement | null {
    return document.querySelector('video');
  }

  /**
   * 获取音频上下文
   */
  protected getAudioContext(): AudioContext {
    return new (window.AudioContext || (window as any).webkitAudioContext)();
  }

  /**
   * 创建媒体源节点
   */
  protected createMediaSourceNode(audioContext: AudioContext, videoElement: HTMLVideoElement): MediaElementAudioSourceNode {
    return audioContext.createMediaElementSource(videoElement);
  }
}

/**
 * 平台适配器工厂
 */
export class PlatformAdapterFactory {
  private static adapters: Map<PlatformType, new() => PlatformAdapter> = new Map();

  /**
   * 注册平台适配器
   */
  static register(platformType: PlatformType, adapterClass: new() => PlatformAdapter): void {
    this.adapters.set(platformType, adapterClass);
  }

  /**
   * 创建适配器实例
   */
  static create(url: string): PlatformAdapter | null {
    for (const [platformType, AdapterClass] of this.adapters) {
      const adapter = new AdapterClass();
      if (adapter.matches(url)) {
        return adapter;
      }
    }
    return null;
  }

  /**
   * 获取所有注册的平台类型
   */
  static getSupportedPlatforms(): PlatformType[] {
    return Array.from(this.adapters.keys());
  }
}
