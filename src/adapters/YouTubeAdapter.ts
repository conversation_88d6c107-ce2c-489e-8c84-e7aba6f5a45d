import { PlatformAdapter } from './PlatformAdapter';
import { IContentInfo, IMediaInfo, ISubtitleTrack, PlatformType, ContentType } from '../types/media';

/**
 * YouTube平台适配器
 */
export class YouTubeAdapter extends PlatformAdapter {
  constructor() {
    super(PlatformType.YOUTUBE);
  }

  matches(url: string): boolean {
    return url.includes('youtube.com') || url.includes('youtu.be');
  }

  async getContentInfo(): Promise<IContentInfo | null> {
    const mediaInfo = await this.extractMediaInfo();
    if (!mediaInfo) return null;

    const subtitles = await this.getSubtitles();
    const contentType = subtitles.length > 0 ? ContentType.SUBTITLE : ContentType.AUDIO_ONLY;

    return {
      platform: this.platformType,
      contentType,
      mediaInfo,
      subtitles,
      metadata: this.extractMetadata()
    };
  }

  async getSubtitles(): Promise<ISubtitleTrack[]> {
    const subtitles: ISubtitleTrack[] = [];
    
    try {
      // 尝试获取YouTube的字幕信息
      const player = this.getYouTubePlayer();
      if (player && player.getOption) {
        const captionTracks = player.getOption('captions', 'tracklist') || [];
        
        for (const track of captionTracks) {
          subtitles.push({
            language: track.languageCode,
            url: track.baseUrl,
            format: 'json',
            isAutoGenerated: track.kind === 'asr'
          });
        }
      }
    } catch (error) {
      console.warn('Failed to extract YouTube subtitles:', error);
    }

    return subtitles;
  }

  async getAudioStream(): Promise<MediaStream | null> {
    const videoElement = this.getVideoElement();
    if (!videoElement) return null;

    try {
      const audioContext = this.getAudioContext();
      const source = this.createMediaSourceNode(audioContext, videoElement);
      
      // 创建音频流捕获
      const destination = audioContext.createMediaStreamDestination();
      source.connect(destination);
      
      return destination.stream;
    } catch (error) {
      console.error('Failed to create audio stream:', error);
      return null;
    }
  }

  injectUI(container: HTMLElement): void {
    const existingUI = document.getElementById('chronotranslate-youtube-ui');
    if (existingUI) return;

    const uiContainer = document.createElement('div');
    uiContainer.id = 'chronotranslate-youtube-ui';
    uiContainer.className = 'chronotranslate-ui';
    
    // YouTube特定的UI样式
    uiContainer.style.cssText = `
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 9999;
      background: rgba(0, 0, 0, 0.8);
      border-radius: 8px;
      padding: 8px;
      color: white;
      font-size: 14px;
    `;

    const toggleButton = document.createElement('button');
    toggleButton.textContent = '翻译';
    toggleButton.className = 'chronotranslate-toggle';
    toggleButton.style.cssText = `
      background: #ff0000;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 6px 12px;
      cursor: pointer;
      font-size: 12px;
    `;

    uiContainer.appendChild(toggleButton);
    container.appendChild(uiContainer);
  }

  cleanup(): void {
    const ui = document.getElementById('chronotranslate-youtube-ui');
    if (ui) {
      ui.remove();
    }
  }

  protected async extractMediaInfo(): Promise<IMediaInfo | null> {
    const videoElement = this.getVideoElement();
    if (!videoElement) return null;

    return {
      url: videoElement.src || window.location.href,
      type: 'video',
      initiator: window.location.href,
      tabId: 0, // 将由background script设置
      requestHeaders: {},
      cookies: document.cookie
    };
  }

  /**
   * 获取YouTube播放器实例
   */
  private getYouTubePlayer(): any {
    // 尝试获取YouTube的内部播放器对象
    const ytInitialPlayerResponse = this.extractYtInitialPlayerResponse();
    if (ytInitialPlayerResponse) {
      return ytInitialPlayerResponse;
    }

    // 备用方法：通过全局变量获取
    return (window as any).ytplayer?.config?.args?.player_response;
  }

  /**
   * 提取YouTube初始播放器响应数据
   */
  private extractYtInitialPlayerResponse(): any {
    try {
      const scripts = document.querySelectorAll('script');
      for (const script of scripts) {
        const content = script.textContent || '';
        const match = content.match(/var ytInitialPlayerResponse = ({.+?});/);
        if (match) {
          return JSON.parse(match[1]);
        }
      }
    } catch (error) {
      console.warn('Failed to extract ytInitialPlayerResponse:', error);
    }
    return null;
  }

  /**
   * 提取视频元数据
   */
  private extractMetadata(): Record<string, any> {
    const title = document.querySelector('h1.title')?.textContent || 
                  document.querySelector('[data-title]')?.getAttribute('data-title') || 
                  document.title;
    
    const channel = document.querySelector('#owner-name a')?.textContent ||
                   document.querySelector('.ytd-channel-name a')?.textContent;

    return {
      title,
      channel,
      url: window.location.href,
      videoId: this.extractVideoId()
    };
  }

  /**
   * 提取视频ID
   */
  private extractVideoId(): string | null {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('v');
  }
}
