import { IContentInfo, ContentType, ISubtitleTrack } from '../types/media';
import { PlatformAdapter } from '../adapters/PlatformAdapter';

/**
 * 内容分析器
 */
export class ContentAnalyzer {
  private adapter: PlatformAdapter;

  constructor(adapter: PlatformAdapter) {
    this.adapter = adapter;
  }

  /**
   * 分析内容类型
   */
  async analyzeContent(): Promise<IContentInfo | null> {
    try {
      const contentInfo = await this.adapter.getContentInfo();
      if (!contentInfo) return null;

      // 进一步分析内容类型
      const refinedContentType = await this.refineContentType(contentInfo);
      contentInfo.contentType = refinedContentType;

      return contentInfo;
    } catch (error) {
      console.error('Content analysis failed:', error);
      return null;
    }
  }

  /**
   * 精确判断内容类型
   */
  private async refineContentType(contentInfo: IContentInfo): Promise<ContentType> {
    // 检查是否有可用字幕
    if (contentInfo.subtitles && contentInfo.subtitles.length > 0) {
      const hasValidSubtitles = await this.validateSubtitles(contentInfo.subtitles);
      if (hasValidSubtitles) {
        return ContentType.SUBTITLE;
      }
    }

    // 检查是否能获取音频流
    const audioStream = await this.adapter.getAudioStream();
    if (audioStream) {
      return ContentType.AUDIO_ONLY;
    }

    // 默认尝试字幕模式
    return ContentType.SUBTITLE;
  }

  /**
   * 验证字幕是否有效
   */
  private async validateSubtitles(subtitles: ISubtitleTrack[]): Promise<boolean> {
    for (const subtitle of subtitles) {
      try {
        if (subtitle.content) {
          // 如果已有内容，检查是否非空
          return subtitle.content.trim().length > 0;
        }

        if (subtitle.url) {
          // 尝试获取字幕内容
          const content = await this.fetchSubtitleContent(subtitle.url);
          if (content && content.trim().length > 0) {
            subtitle.content = content;
            return true;
          }
        }
      } catch (error) {
        console.warn('Failed to validate subtitle:', error);
        continue;
      }
    }

    return false;
  }

  /**
   * 获取字幕内容
   */
  private async fetchSubtitleContent(url: string): Promise<string | null> {
    try {
      const response = await fetch(url);
      if (!response.ok) return null;
      
      const content = await response.text();
      return content;
    } catch (error) {
      console.error('Failed to fetch subtitle content:', error);
      return null;
    }
  }

  /**
   * 获取推荐的处理策略
   */
  getProcessingStrategy(contentInfo: IContentInfo): ProcessingStrategy {
    switch (contentInfo.contentType) {
      case ContentType.SUBTITLE:
        return {
          type: 'subtitle',
          priority: ['existing_subtitles', 'auto_generated_subtitles'],
          fallback: 'audio_recognition'
        };
      
      case ContentType.AUDIO_ONLY:
        return {
          type: 'audio',
          priority: ['real_time_asr', 'buffered_asr'],
          fallback: 'none'
        };
      
      default:
        return {
          type: 'hybrid',
          priority: ['existing_subtitles', 'real_time_asr'],
          fallback: 'none'
        };
    }
  }
}

/**
 * 处理策略接口
 */
export interface ProcessingStrategy {
  type: 'subtitle' | 'audio' | 'hybrid';
  priority: string[];
  fallback: string;
}

/**
 * 内容质量评估器
 */
export class ContentQualityAssessor {
  /**
   * 评估字幕质量
   */
  static assessSubtitleQuality(subtitle: ISubtitleTrack): QualityScore {
    let score = 0;
    const factors: string[] = [];

    // 检查是否为自动生成
    if (!subtitle.isAutoGenerated) {
      score += 30;
      factors.push('human_generated');
    } else {
      factors.push('auto_generated');
    }

    // 检查内容长度
    if (subtitle.content) {
      const wordCount = subtitle.content.split(/\s+/).length;
      if (wordCount > 100) {
        score += 25;
        factors.push('sufficient_content');
      } else if (wordCount > 20) {
        score += 15;
        factors.push('moderate_content');
      }
    }

    // 检查格式
    if (['srt', 'vtt'].includes(subtitle.format)) {
      score += 20;
      factors.push('standard_format');
    }

    // 检查语言
    if (subtitle.language && subtitle.language !== 'auto') {
      score += 15;
      factors.push('known_language');
    }

    // 检查URL可用性
    if (subtitle.url) {
      score += 10;
      factors.push('accessible_url');
    }

    return {
      score: Math.min(score, 100),
      factors,
      recommendation: score >= 70 ? 'use' : score >= 40 ? 'use_with_caution' : 'avoid'
    };
  }

  /**
   * 评估音频质量
   */
  static assessAudioQuality(audioStream: MediaStream): QualityScore {
    const audioTracks = audioStream.getAudioTracks();
    if (audioTracks.length === 0) {
      return {
        score: 0,
        factors: ['no_audio_tracks'],
        recommendation: 'avoid'
      };
    }

    let score = 50; // 基础分
    const factors: string[] = [];

    const track = audioTracks[0];
    const settings = track.getSettings();

    // 检查采样率
    if (settings.sampleRate && settings.sampleRate >= 44100) {
      score += 25;
      factors.push('high_sample_rate');
    } else if (settings.sampleRate && settings.sampleRate >= 22050) {
      score += 15;
      factors.push('moderate_sample_rate');
    }

    // 检查声道数
    if (settings.channelCount && settings.channelCount >= 2) {
      score += 15;
      factors.push('stereo_audio');
    } else if (settings.channelCount === 1) {
      score += 10;
      factors.push('mono_audio');
    }

    // 检查音轨状态
    if (track.enabled && track.readyState === 'live') {
      score += 10;
      factors.push('active_track');
    }

    return {
      score: Math.min(score, 100),
      factors,
      recommendation: score >= 70 ? 'use' : score >= 40 ? 'use_with_caution' : 'avoid'
    };
  }
}

/**
 * 质量评分接口
 */
export interface QualityScore {
  score: number;
  factors: string[];
  recommendation: 'use' | 'use_with_caution' | 'avoid';
}
