import { ContentAnalyzer, ProcessingStrategy } from '../analyzers/ContentAnalyzer';
import { PlatformAdapter, PlatformAdapterFactory } from '../adapters/PlatformAdapter';
import { PipelineScheduler } from '../pipeline/PipelineScheduler';
import { TranslationEngineManager } from '../services/TranslationEngine';
import { TTSEngineManager } from '../services/TTSEngine';
import { AudioSyncPlayer, AudioSyncPlayerFactory, TTSAudioItemBuilder } from '../services/AudioSyncPlayer';
import { IContentInfo, ContentType, IRecognitionResult } from '../types/media';
import { SubtitleExtractionNode, SubtitleSyncNode, SubtitleFilterNode, SubtitleFilters, ISubtitleEntry } from '../pipeline/nodes/SubtitleProcessingNode';
import { AudioCaptureNode, SpeechRecognitionNode, WebSpeechRecognitionService } from '../pipeline/nodes/AudioProcessingNode';

/**
 * 同声传译控制器 - 协调整个同声传译流程
 */
export class TranslationController {
  private adapter: PlatformAdapter | null = null;
  private contentAnalyzer: ContentAnalyzer | null = null;
  private pipelineScheduler: PipelineScheduler;
  private translationEngine: TranslationEngineManager;
  private ttsEngine: TTSEngineManager;
  private audioPlayer: AudioSyncPlayer;
  private isActive = false;
  private currentStrategy: ProcessingStrategy | null = null;
  private targetLanguage: string = 'zh-CN';

  constructor() {
    this.pipelineScheduler = new PipelineScheduler();
    this.translationEngine = new TranslationEngineManager();
    this.ttsEngine = new TTSEngineManager();
    this.audioPlayer = AudioSyncPlayerFactory.getInstance();
    this.initializePipelines();
  }

  /**
   * 初始化翻译流程
   */
  async initialize(url: string): Promise<boolean> {
    try {
      // 创建平台适配器
      this.adapter = PlatformAdapterFactory.create(url);
      if (!this.adapter) {
        throw new Error('Unsupported platform');
      }

      // 创建内容分析器
      this.contentAnalyzer = new ContentAnalyzer(this.adapter);

      // 分析内容
      const contentInfo = await this.contentAnalyzer.analyzeContent();
      if (!contentInfo) {
        throw new Error('Failed to analyze content');
      }

      // 获取处理策略
      this.currentStrategy = this.contentAnalyzer.getProcessingStrategy(contentInfo);

      console.log('Translation controller initialized:', {
        platform: contentInfo.platform,
        contentType: contentInfo.contentType,
        strategy: this.currentStrategy
      });

      return true;
    } catch (error) {
      console.error('Failed to initialize translation controller:', error);
      return false;
    }
  }

  /**
   * 启动同声传译
   */
  async startTranslation(targetLanguage: string): Promise<void> {
    if (!this.adapter || !this.contentAnalyzer || !this.currentStrategy) {
      throw new Error('Controller not initialized');
    }

    if (this.isActive) {
      console.warn('Translation already active');
      return;
    }

    this.isActive = true;
    this.targetLanguage = targetLanguage;

    try {
      const contentInfo = await this.contentAnalyzer.analyzeContent();
      if (!contentInfo) {
        throw new Error('Failed to get content info');
      }

      // 设置视频元素
      const videoElement = document.querySelector('video');
      if (videoElement) {
        this.audioPlayer.setVideoElement(videoElement);
      }

      // 注入UI
      this.injectTranslationUI();

      // 启动音频播放器
      this.audioPlayer.start();

      // 根据内容类型选择处理流程
      switch (contentInfo.contentType) {
        case ContentType.SUBTITLE:
          await this.processSubtitleContent(contentInfo, targetLanguage);
          break;
        case ContentType.AUDIO_ONLY:
          await this.processAudioContent(contentInfo, targetLanguage);
          break;
        default:
          throw new Error(`Unsupported content type: ${contentInfo.contentType}`);
      }
    } catch (error) {
      console.error('Translation failed:', error);
      this.isActive = false;
      throw error;
    }
  }

  /**
   * 停止同声传译
   */
  async stopTranslation(): Promise<void> {
    if (!this.isActive) return;

    this.isActive = false;

    // 停止音频播放器
    this.audioPlayer.stop();

    // 清理资源
    if (this.adapter) {
      this.adapter.cleanup();
    }

    // 移除UI
    this.removeTranslationUI();

    console.log('Simultaneous interpretation stopped');
  }

  /**
   * 处理字幕内容 - 字幕 → 翻译 → TTS → 同步播放
   */
  private async processSubtitleContent(contentInfo: IContentInfo, targetLanguage: string): Promise<void> {
    if (!contentInfo.subtitles || contentInfo.subtitles.length === 0) {
      throw new Error('No subtitles available');
    }

    // 选择最佳字幕轨道
    const bestSubtitle = this.selectBestSubtitle(contentInfo.subtitles);

    try {
      // 执行字幕处理管道
      const subtitleEntries = await this.pipelineScheduler.execute('subtitle-processing', bestSubtitle) as ISubtitleEntry[];

      // 处理每个字幕条目：翻译 → TTS → 添加到播放队列
      for (const entry of subtitleEntries) {
        await this.processSubtitleEntry(entry, targetLanguage);
      }

    } catch (error) {
      console.error('Subtitle processing failed:', error);
      throw error;
    }
  }

  /**
   * 处理单个字幕条目
   */
  private async processSubtitleEntry(entry: ISubtitleEntry, targetLanguage: string): Promise<void> {
    try {
      // 1. 翻译文本
      const translationResult = await this.translationEngine.translate(
        entry.text,
        targetLanguage
      );

      // 2. TTS合成
      const ttsResult = await this.ttsEngine.synthesize(
        translationResult.translatedText,
        targetLanguage
      );

      // 3. 创建音频项并添加到播放队列
      const audioItem = TTSAudioItemBuilder.fromSubtitleEntry(
        ttsResult,
        entry,
        translationResult.translatedText,
        targetLanguage
      );

      this.audioPlayer.addAudioItem(audioItem);

      console.log(`Processed subtitle: "${entry.text}" → "${translationResult.translatedText}"`);
    } catch (error) {
      console.error(`Failed to process subtitle entry:`, error);
    }
  }

  /**
   * 处理音频内容 - 音频识别 → 翻译 → TTS → 同步播放
   */
  private async processAudioContent(contentInfo: IContentInfo, targetLanguage: string): Promise<void> {
    if (!this.adapter) return;

    try {
      // 获取音频流
      const audioStream = await this.adapter.getAudioStream();
      if (!audioStream) {
        throw new Error('Failed to get audio stream');
      }

      // 启动音频捕获节点
      const audioCaptureNode = new AudioCaptureNode();
      await audioCaptureNode.initialize();

      // 开始处理音频流
      this.startAudioProcessingLoop(audioStream, targetLanguage);

    } catch (error) {
      console.error('Audio processing failed:', error);
      throw error;
    }
  }

  /**
   * 音频处理循环
   */
  private async startAudioProcessingLoop(audioStream: MediaStream, targetLanguage: string): Promise<void> {
    const audioCaptureNode = new AudioCaptureNode();
    const speechRecognitionNode = new SpeechRecognitionNode(new WebSpeechRecognitionService());

    try {
      // 处理音频流
      const audioSegments = await audioCaptureNode.process(audioStream);

      // 处理每个音频段
      for (const segment of audioSegments) {
        await this.processAudioSegment(segment, speechRecognitionNode, targetLanguage);
      }
    } catch (error) {
      console.error('Audio processing loop failed:', error);
    }
  }

  /**
   * 处理单个音频段
   */
  private async processAudioSegment(
    segment: any,
    speechRecognitionNode: SpeechRecognitionNode,
    targetLanguage: string
  ): Promise<void> {
    try {
      // 1. 语音识别
      const recognitionResult = await speechRecognitionNode.process(segment) as IRecognitionResult | null;

      if (!recognitionResult || !recognitionResult.text) {
        return;
      }

      // 2. 翻译文本
      const translationResult = await this.translationEngine.translate(
        recognitionResult.text,
        targetLanguage,
        recognitionResult.language
      );

      // 3. TTS合成
      const ttsResult = await this.ttsEngine.synthesize(
        translationResult.translatedText,
        targetLanguage
      );

      // 4. 创建音频项并添加到播放队列
      const audioItem = TTSAudioItemBuilder.fromTTSResult(
        ttsResult,
        recognitionResult.startTime,
        recognitionResult.text,
        translationResult.translatedText,
        targetLanguage
      );

      this.audioPlayer.addAudioItem(audioItem);

      console.log(`Processed audio: "${recognitionResult.text}" → "${translationResult.translatedText}"`);
    } catch (error) {
      console.error('Failed to process audio segment:', error);
    }
  }

  /**
   * 初始化处理管道
   */
  private initializePipelines(): void {
    // 字幕处理管道
    this.pipelineScheduler.registerPipeline('subtitle-processing', [
      new SubtitleExtractionNode(),
      new SubtitleSyncNode(),
      new SubtitleFilterNode([
        SubtitleFilters.nonEmpty,
        SubtitleFilters.minDuration(0.5),
        SubtitleFilters.noMusicSymbols
      ])
    ]);

    // 音频捕获管道
    this.pipelineScheduler.registerPipeline('audio-capture', [
      new AudioCaptureNode()
    ]);

    // 语音识别管道
    this.pipelineScheduler.registerPipeline('speech-recognition', [
      new SpeechRecognitionNode(new WebSpeechRecognitionService())
    ]);
  }

  /**
   * 选择最佳字幕轨道
   */
  private selectBestSubtitle(subtitles: any[]): any {
    // 优先选择非自动生成的字幕
    const manualSubtitles = subtitles.filter(sub => !sub.isAutoGenerated);
    if (manualSubtitles.length > 0) {
      return manualSubtitles[0];
    }

    // 否则选择第一个可用字幕
    return subtitles[0];
  }



  /**
   * 注入翻译UI
   */
  private injectTranslationUI(): void {
    if (!this.adapter) return;

    const container = document.body;
    this.adapter.injectUI(container);
  }

  /**
   * 移除翻译UI
   */
  private removeTranslationUI(): void {
    const elements = [
      'chronotranslate-subtitles',
      'chronotranslate-realtime'
    ];

    elements.forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        element.remove();
      }
    });

    if (this.adapter) {
      this.adapter.cleanup();
    }
  }

  /**
   * 获取当前状态
   */
  getStatus(): TranslationStatus {
    return {
      isActive: this.isActive,
      platform: this.adapter?.constructor.name || null,
      strategy: this.currentStrategy?.type || null,
      supportedPlatforms: PlatformAdapterFactory.getSupportedPlatforms()
    };
  }
}

/**
 * 翻译状态接口
 */
export interface TranslationStatus {
  isActive: boolean;
  platform: string | null;
  strategy: string | null;
  supportedPlatforms: string[];
}
