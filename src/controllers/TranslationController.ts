import { ContentAnalyzer, ProcessingStrategy } from '../analyzers/ContentAnalyzer';
import { PlatformAdapter, PlatformAdapterFactory } from '../adapters/PlatformAdapter';
import { PipelineScheduler } from '../pipeline/PipelineScheduler';
import { TranslationEngineManager } from '../services/TranslationEngine';
import { IContentInfo, ContentType, ITranslationResult } from '../types/media';
import { SubtitleExtractionNode, SubtitleSyncNode, SubtitleFilterNode, SubtitleFilters } from '../pipeline/nodes/SubtitleProcessingNode';
import { AudioCaptureNode, SpeechRecognitionNode, WebSpeechRecognitionService } from '../pipeline/nodes/AudioProcessingNode';

/**
 * 翻译控制器 - 协调整个翻译流程
 */
export class TranslationController {
  private adapter: PlatformAdapter | null = null;
  private contentAnalyzer: ContentAnalyzer | null = null;
  private pipelineScheduler: PipelineScheduler;
  private translationEngine: TranslationEngineManager;
  private isActive = false;
  private currentStrategy: ProcessingStrategy | null = null;

  constructor() {
    this.pipelineScheduler = new PipelineScheduler();
    this.translationEngine = new TranslationEngineManager();
    this.initializePipelines();
  }

  /**
   * 初始化翻译流程
   */
  async initialize(url: string): Promise<boolean> {
    try {
      // 创建平台适配器
      this.adapter = PlatformAdapterFactory.create(url);
      if (!this.adapter) {
        throw new Error('Unsupported platform');
      }

      // 创建内容分析器
      this.contentAnalyzer = new ContentAnalyzer(this.adapter);

      // 分析内容
      const contentInfo = await this.contentAnalyzer.analyzeContent();
      if (!contentInfo) {
        throw new Error('Failed to analyze content');
      }

      // 获取处理策略
      this.currentStrategy = this.contentAnalyzer.getProcessingStrategy(contentInfo);

      console.log('Translation controller initialized:', {
        platform: contentInfo.platform,
        contentType: contentInfo.contentType,
        strategy: this.currentStrategy
      });

      return true;
    } catch (error) {
      console.error('Failed to initialize translation controller:', error);
      return false;
    }
  }

  /**
   * 启动翻译
   */
  async startTranslation(targetLanguage: string): Promise<void> {
    if (!this.adapter || !this.contentAnalyzer || !this.currentStrategy) {
      throw new Error('Controller not initialized');
    }

    if (this.isActive) {
      console.warn('Translation already active');
      return;
    }

    this.isActive = true;

    try {
      const contentInfo = await this.contentAnalyzer.analyzeContent();
      if (!contentInfo) {
        throw new Error('Failed to get content info');
      }

      // 注入UI
      this.injectTranslationUI();

      // 根据内容类型选择处理流程
      switch (contentInfo.contentType) {
        case ContentType.SUBTITLE:
          await this.processSubtitleContent(contentInfo, targetLanguage);
          break;
        case ContentType.AUDIO_ONLY:
          await this.processAudioContent(contentInfo, targetLanguage);
          break;
        default:
          throw new Error(`Unsupported content type: ${contentInfo.contentType}`);
      }
    } catch (error) {
      console.error('Translation failed:', error);
      this.isActive = false;
      throw error;
    }
  }

  /**
   * 停止翻译
   */
  async stopTranslation(): Promise<void> {
    if (!this.isActive) return;

    this.isActive = false;

    // 清理资源
    if (this.adapter) {
      this.adapter.cleanup();
    }

    // 移除UI
    this.removeTranslationUI();

    console.log('Translation stopped');
  }

  /**
   * 处理字幕内容
   */
  private async processSubtitleContent(contentInfo: IContentInfo, targetLanguage: string): Promise<void> {
    if (!contentInfo.subtitles || contentInfo.subtitles.length === 0) {
      throw new Error('No subtitles available');
    }

    // 选择最佳字幕轨道
    const bestSubtitle = this.selectBestSubtitle(contentInfo.subtitles);
    
    try {
      // 执行字幕处理管道
      const subtitleEntries = await this.pipelineScheduler.execute('subtitle-processing', bestSubtitle);
      
      // 翻译字幕
      const translatedEntries = await this.translateSubtitleEntries(subtitleEntries, targetLanguage);
      
      // 显示翻译结果
      this.displayTranslatedSubtitles(translatedEntries);
      
    } catch (error) {
      console.error('Subtitle processing failed:', error);
      throw error;
    }
  }

  /**
   * 处理音频内容
   */
  private async processAudioContent(contentInfo: IContentInfo, targetLanguage: string): Promise<void> {
    if (!this.adapter) return;

    try {
      // 获取音频流
      const audioStream = await this.adapter.getAudioStream();
      if (!audioStream) {
        throw new Error('Failed to get audio stream');
      }

      // 执行音频处理管道
      const audioSegments = await this.pipelineScheduler.execute('audio-capture', audioStream);
      
      // 处理每个音频段
      for (const segment of audioSegments) {
        const recognitionResult = await this.pipelineScheduler.execute('speech-recognition', segment);
        
        if (recognitionResult && recognitionResult.text) {
          // 翻译识别结果
          const translationResult = await this.translationEngine.translate(
            recognitionResult.text,
            targetLanguage,
            recognitionResult.language
          );
          
          // 显示翻译结果
          this.displayTranslationResult(translationResult, recognitionResult.startTime);
        }
      }
    } catch (error) {
      console.error('Audio processing failed:', error);
      throw error;
    }
  }

  /**
   * 初始化处理管道
   */
  private initializePipelines(): void {
    // 字幕处理管道
    this.pipelineScheduler.registerPipeline('subtitle-processing', [
      new SubtitleExtractionNode(),
      new SubtitleSyncNode(),
      new SubtitleFilterNode([
        SubtitleFilters.nonEmpty,
        SubtitleFilters.minDuration(0.5),
        SubtitleFilters.noMusicSymbols
      ])
    ]);

    // 音频捕获管道
    this.pipelineScheduler.registerPipeline('audio-capture', [
      new AudioCaptureNode()
    ]);

    // 语音识别管道
    this.pipelineScheduler.registerPipeline('speech-recognition', [
      new SpeechRecognitionNode(new WebSpeechRecognitionService())
    ]);
  }

  /**
   * 选择最佳字幕轨道
   */
  private selectBestSubtitle(subtitles: any[]): any {
    // 优先选择非自动生成的字幕
    const manualSubtitles = subtitles.filter(sub => !sub.isAutoGenerated);
    if (manualSubtitles.length > 0) {
      return manualSubtitles[0];
    }

    // 否则选择第一个可用字幕
    return subtitles[0];
  }

  /**
   * 翻译字幕条目
   */
  private async translateSubtitleEntries(entries: any[], targetLanguage: string): Promise<any[]> {
    const translatedEntries = [];

    for (const entry of entries) {
      try {
        const translationResult = await this.translationEngine.translate(
          entry.text,
          targetLanguage
        );

        translatedEntries.push({
          ...entry,
          originalText: entry.text,
          translatedText: translationResult.translatedText,
          translationResult
        });
      } catch (error) {
        console.warn('Failed to translate entry:', error);
        // 保留原文
        translatedEntries.push({
          ...entry,
          originalText: entry.text,
          translatedText: entry.text,
          translationResult: null
        });
      }
    }

    return translatedEntries;
  }

  /**
   * 显示翻译后的字幕
   */
  private displayTranslatedSubtitles(entries: any[]): void {
    // 创建字幕显示容器
    let subtitleContainer = document.getElementById('chronotranslate-subtitles');
    if (!subtitleContainer) {
      subtitleContainer = document.createElement('div');
      subtitleContainer.id = 'chronotranslate-subtitles';
      subtitleContainer.style.cssText = `
        position: fixed;
        bottom: 100px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 10000;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 10px 20px;
        border-radius: 8px;
        font-size: 16px;
        max-width: 80%;
        text-align: center;
        pointer-events: none;
      `;
      document.body.appendChild(subtitleContainer);
    }

    // 监听视频时间更新
    const videoElement = document.querySelector('video');
    if (videoElement) {
      videoElement.addEventListener('timeupdate', () => {
        const currentTime = videoElement.currentTime;
        const currentEntry = entries.find(entry => 
          currentTime >= entry.startTime && currentTime <= entry.endTime
        );

        if (currentEntry) {
          subtitleContainer!.textContent = currentEntry.translatedText;
          subtitleContainer!.style.display = 'block';
        } else {
          subtitleContainer!.style.display = 'none';
        }
      });
    }
  }

  /**
   * 显示翻译结果
   */
  private displayTranslationResult(result: ITranslationResult, timestamp: number): void {
    // 创建实时翻译显示
    let translationContainer = document.getElementById('chronotranslate-realtime');
    if (!translationContainer) {
      translationContainer = document.createElement('div');
      translationContainer.id = 'chronotranslate-realtime';
      translationContainer.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 15px;
        border-radius: 8px;
        font-size: 14px;
        max-width: 300px;
        border-left: 4px solid #4CAF50;
      `;
      document.body.appendChild(translationContainer);
    }

    // 显示翻译结果
    translationContainer.innerHTML = `
      <div style="margin-bottom: 8px; color: #ccc; font-size: 12px;">
        ${new Date(timestamp).toLocaleTimeString()}
      </div>
      <div style="margin-bottom: 5px; font-weight: bold;">
        ${result.translatedText}
      </div>
      <div style="color: #aaa; font-size: 12px;">
        原文: ${result.originalText}
      </div>
    `;

    // 3秒后淡出
    setTimeout(() => {
      if (translationContainer) {
        translationContainer.style.opacity = '0.5';
      }
    }, 3000);
  }

  /**
   * 注入翻译UI
   */
  private injectTranslationUI(): void {
    if (!this.adapter) return;

    const container = document.body;
    this.adapter.injectUI(container);
  }

  /**
   * 移除翻译UI
   */
  private removeTranslationUI(): void {
    const elements = [
      'chronotranslate-subtitles',
      'chronotranslate-realtime'
    ];

    elements.forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        element.remove();
      }
    });

    if (this.adapter) {
      this.adapter.cleanup();
    }
  }

  /**
   * 获取当前状态
   */
  getStatus(): TranslationStatus {
    return {
      isActive: this.isActive,
      platform: this.adapter?.constructor.name || null,
      strategy: this.currentStrategy?.type || null,
      supportedPlatforms: PlatformAdapterFactory.getSupportedPlatforms()
    };
  }
}

/**
 * 翻译状态接口
 */
export interface TranslationStatus {
  isActive: boolean;
  platform: string | null;
  strategy: string | null;
  supportedPlatforms: string[];
}
