import { ITTSResult } from '../types/media';

/**
 * TTS引擎接口
 */
export interface ITTSEngine {
  synthesize(text: string, language: string, voice?: string): Promise<ITTSResult>;
  getSupportedLanguages(): Promise<string[]>;
  getSupportedVoices(language: string): Promise<ITTSVoice[]>;
  getName(): string;
  isAvailable(): Promise<boolean>;
}

/**
 * TTS语音选项接口
 */
export interface ITTSVoice {
  id: string;
  name: string;
  language: string;
  gender: 'male' | 'female' | 'neutral';
  quality: 'standard' | 'premium' | 'neural';
}

/**
 * TTS引擎管理器
 */
export class TTSEngineManager {
  private engines: Map<string, ITTSEngine> = new Map();
  private primaryEngine: string | null = null;
  private fallbackEngines: string[] = [];
  private voiceSettings: Map<string, ITTSVoice> = new Map(); // 语言 -> 语音设置

  /**
   * 注册TTS引擎
   */
  registerEngine(name: string, engine: ITTSEngine): void {
    this.engines.set(name, engine);
    
    if (!this.primaryEngine) {
      this.primaryEngine = name;
    }
  }

  /**
   * 设置主引擎
   */
  setPrimaryEngine(name: string): void {
    if (this.engines.has(name)) {
      this.primaryEngine = name;
    } else {
      throw new Error(`TTS engine '${name}' not found`);
    }
  }

  /**
   * 设置备用引擎
   */
  setFallbackEngines(engines: string[]): void {
    this.fallbackEngines = engines.filter(name => this.engines.has(name));
  }

  /**
   * 合成语音
   */
  async synthesize(text: string, language: string): Promise<ITTSResult> {
    const engineNames = [this.primaryEngine, ...this.fallbackEngines].filter(Boolean) as string[];
    
    for (const engineName of engineNames) {
      const engine = this.engines.get(engineName);
      if (!engine) continue;

      try {
        const isAvailable = await engine.isAvailable();
        if (!isAvailable) continue;

        // 获取语音设置
        const voice = this.voiceSettings.get(language);
        const result = await engine.synthesize(text, language, voice?.id);
        
        return result;
      } catch (error) {
        console.warn(`TTS synthesis failed with engine ${engineName}:`, error);
        continue;
      }
    }

    throw new Error('All TTS engines failed');
  }

  /**
   * 设置语音偏好
   */
  async setVoicePreference(language: string, voiceId: string): Promise<void> {
    // 查找对应的语音信息
    for (const engine of this.engines.values()) {
      try {
        const voices = await engine.getSupportedVoices(language);
        const voice = voices.find(v => v.id === voiceId);
        if (voice) {
          this.voiceSettings.set(language, voice);
          return;
        }
      } catch (error) {
        continue;
      }
    }
  }

  /**
   * 获取可用引擎列表
   */
  getAvailableEngines(): string[] {
    return Array.from(this.engines.keys());
  }
}

/**
 * Google TTS引擎
 */
export class GoogleTTSEngine implements ITTSEngine {
  private apiKey: string;
  private baseUrl = 'https://texttospeech.googleapis.com/v1/text:synthesize';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async synthesize(text: string, language: string, voice?: string): Promise<ITTSResult> {
    const requestBody = {
      input: { text },
      voice: {
        languageCode: language,
        name: voice || this.getDefaultVoice(language),
        ssmlGender: 'NEUTRAL'
      },
      audioConfig: {
        audioEncoding: 'MP3',
        speakingRate: 1.0,
        pitch: 0.0,
        volumeGainDb: 0.0
      }
    };

    const response = await fetch(`${this.baseUrl}?key=${this.apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`Google TTS API error: ${response.status}`);
    }

    const data = await response.json();
    const audioBuffer = this.base64ToArrayBuffer(data.audioContent);

    return {
      audioBuffer,
      duration: await this.estimateAudioDuration(audioBuffer),
      format: 'mp3',
      provider: 'google'
    };
  }

  async getSupportedLanguages(): Promise<string[]> {
    const response = await fetch(`https://texttospeech.googleapis.com/v1/voices?key=${this.apiKey}`);
    if (!response.ok) {
      throw new Error(`Failed to get supported languages: ${response.status}`);
    }

    const data = await response.json();
    const languages = new Set<string>();
    
    data.voices.forEach((voice: any) => {
      languages.add(voice.languageCodes[0]);
    });

    return Array.from(languages);
  }

  async getSupportedVoices(language: string): Promise<ITTSVoice[]> {
    const response = await fetch(`https://texttospeech.googleapis.com/v1/voices?key=${this.apiKey}`);
    if (!response.ok) {
      throw new Error(`Failed to get supported voices: ${response.status}`);
    }

    const data = await response.json();
    const voices: ITTSVoice[] = [];

    data.voices.forEach((voice: any) => {
      if (voice.languageCodes.includes(language)) {
        voices.push({
          id: voice.name,
          name: voice.name,
          language: voice.languageCodes[0],
          gender: voice.ssmlGender.toLowerCase(),
          quality: voice.name.includes('Neural') ? 'neural' : 'standard'
        });
      }
    });

    return voices;
  }

  getName(): string {
    return 'google';
  }

  async isAvailable(): Promise<boolean> {
    try {
      const response = await fetch(`https://texttospeech.googleapis.com/v1/voices?key=${this.apiKey}`);
      return response.ok;
    } catch {
      return false;
    }
  }

  private getDefaultVoice(language: string): string {
    const defaultVoices: Record<string, string> = {
      'en-US': 'en-US-Neural2-A',
      'zh-CN': 'zh-CN-Neural2-A',
      'ja-JP': 'ja-JP-Neural2-A',
      'ko-KR': 'ko-KR-Neural2-A',
      'es-ES': 'es-ES-Neural2-A',
      'fr-FR': 'fr-FR-Neural2-A',
      'de-DE': 'de-DE-Neural2-A'
    };
    return defaultVoices[language] || `${language}-Standard-A`;
  }

  private base64ToArrayBuffer(base64: string): ArrayBuffer {
    const binaryString = atob(base64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes.buffer;
  }

  private async estimateAudioDuration(audioBuffer: ArrayBuffer): Promise<number> {
    // 简单估算：假设平均语速为每分钟150词，每个字符0.1秒
    // 实际应该通过音频解码获取准确时长
    return audioBuffer.byteLength / 1000; // 粗略估算
  }
}

/**
 * Azure TTS引擎
 */
export class AzureTTSEngine implements ITTSEngine {
  private apiKey: string;
  private region: string;
  private baseUrl: string;

  constructor(apiKey: string, region: string = 'eastus') {
    this.apiKey = apiKey;
    this.region = region;
    this.baseUrl = `https://${region}.tts.speech.microsoft.com/cognitiveservices/v1`;
  }

  async synthesize(text: string, language: string, voice?: string): Promise<ITTSResult> {
    const ssml = this.buildSSML(text, language, voice);
    
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Ocp-Apim-Subscription-Key': this.apiKey,
        'Content-Type': 'application/ssml+xml',
        'X-Microsoft-OutputFormat': 'audio-16khz-128kbitrate-mono-mp3'
      },
      body: ssml
    });

    if (!response.ok) {
      throw new Error(`Azure TTS API error: ${response.status}`);
    }

    const audioBuffer = await response.arrayBuffer();

    return {
      audioBuffer,
      duration: await this.estimateAudioDuration(audioBuffer),
      format: 'mp3',
      provider: 'azure'
    };
  }

  async getSupportedLanguages(): Promise<string[]> {
    const response = await fetch(`https://${this.region}.tts.speech.microsoft.com/cognitiveservices/voices/list`, {
      headers: {
        'Ocp-Apim-Subscription-Key': this.apiKey
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to get supported languages: ${response.status}`);
    }

    const voices = await response.json();
    const languages = new Set<string>();
    
    voices.forEach((voice: any) => {
      languages.add(voice.Locale);
    });

    return Array.from(languages);
  }

  async getSupportedVoices(language: string): Promise<ITTSVoice[]> {
    const response = await fetch(`https://${this.region}.tts.speech.microsoft.com/cognitiveservices/voices/list`, {
      headers: {
        'Ocp-Apim-Subscription-Key': this.apiKey
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to get supported voices: ${response.status}`);
    }

    const voices = await response.json();
    const supportedVoices: ITTSVoice[] = [];

    voices.forEach((voice: any) => {
      if (voice.Locale === language) {
        supportedVoices.push({
          id: voice.ShortName,
          name: voice.DisplayName,
          language: voice.Locale,
          gender: voice.Gender.toLowerCase(),
          quality: voice.VoiceType === 'Neural' ? 'neural' : 'standard'
        });
      }
    });

    return supportedVoices;
  }

  getName(): string {
    return 'azure';
  }

  async isAvailable(): Promise<boolean> {
    try {
      const response = await fetch(`https://${this.region}.tts.speech.microsoft.com/cognitiveservices/voices/list`, {
        headers: {
          'Ocp-Apim-Subscription-Key': this.apiKey
        }
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  private buildSSML(text: string, language: string, voice?: string): string {
    const voiceName = voice || this.getDefaultVoice(language);
    return `
      <speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="${language}">
        <voice name="${voiceName}">
          ${text}
        </voice>
      </speak>
    `.trim();
  }

  private getDefaultVoice(language: string): string {
    const defaultVoices: Record<string, string> = {
      'en-US': 'en-US-AriaNeural',
      'zh-CN': 'zh-CN-XiaoxiaoNeural',
      'ja-JP': 'ja-JP-NanamiNeural',
      'ko-KR': 'ko-KR-SunHiNeural',
      'es-ES': 'es-ES-ElviraNeural',
      'fr-FR': 'fr-FR-DeniseNeural',
      'de-DE': 'de-DE-KatjaNeural'
    };
    return defaultVoices[language] || `${language}-Standard`;
  }

  private async estimateAudioDuration(audioBuffer: ArrayBuffer): Promise<number> {
    return audioBuffer.byteLength / 1000; // 粗略估算
  }
}

/**
 * 浏览器内置TTS引擎
 */
export class BrowserTTSEngine implements ITTSEngine {
  private synthesis: SpeechSynthesis;
  private voices: SpeechSynthesisVoice[] = [];

  constructor() {
    this.synthesis = window.speechSynthesis;
    this.loadVoices();
  }

  async synthesize(text: string, language: string, voice?: string): Promise<ITTSResult> {
    return new Promise((resolve, reject) => {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = language;
      
      // 选择语音
      if (voice) {
        const selectedVoice = this.voices.find(v => v.name === voice || v.voiceURI === voice);
        if (selectedVoice) {
          utterance.voice = selectedVoice;
        }
      }

      // 录制音频
      const audioChunks: Blob[] = [];
      const mediaRecorder = this.createMediaRecorder(audioChunks);

      utterance.onstart = () => {
        mediaRecorder.start();
      };

      utterance.onend = () => {
        mediaRecorder.stop();
        setTimeout(() => {
          const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
          audioBlob.arrayBuffer().then(buffer => {
            resolve({
              audioBuffer: buffer,
              duration: this.estimateTextDuration(text),
              format: 'wav',
              provider: 'browser'
            });
          });
        }, 100);
      };

      utterance.onerror = (event) => {
        reject(new Error(`Browser TTS error: ${event.error}`));
      };

      this.synthesis.speak(utterance);
    });
  }

  async getSupportedLanguages(): Promise<string[]> {
    await this.loadVoices();
    const languages = new Set<string>();
    this.voices.forEach(voice => {
      languages.add(voice.lang);
    });
    return Array.from(languages);
  }

  async getSupportedVoices(language: string): Promise<ITTSVoice[]> {
    await this.loadVoices();
    return this.voices
      .filter(voice => voice.lang === language)
      .map(voice => ({
        id: voice.voiceURI,
        name: voice.name,
        language: voice.lang,
        gender: 'neutral' as const,
        quality: 'standard' as const
      }));
  }

  getName(): string {
    return 'browser';
  }

  async isAvailable(): Promise<boolean> {
    return 'speechSynthesis' in window;
  }

  private async loadVoices(): Promise<void> {
    return new Promise((resolve) => {
      const voices = this.synthesis.getVoices();
      if (voices.length > 0) {
        this.voices = voices;
        resolve();
      } else {
        this.synthesis.onvoiceschanged = () => {
          this.voices = this.synthesis.getVoices();
          resolve();
        };
      }
    });
  }

  private createMediaRecorder(audioChunks: Blob[]): MediaRecorder {
    // 创建音频上下文来录制TTS输出
    const audioContext = new AudioContext();
    const destination = audioContext.createMediaStreamDestination();
    const mediaRecorder = new MediaRecorder(destination.stream);

    mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        audioChunks.push(event.data);
      }
    };

    return mediaRecorder;
  }

  private estimateTextDuration(text: string): number {
    // 估算语音时长：平均每分钟150词
    const words = text.split(/\s+/).length;
    return (words / 150) * 60;
  }
}
