import { ITTSResult } from '../types/media';

/**
 * 音频同步播放项
 */
export interface IAudioSyncItem {
  id: string;
  audioBuffer: ArrayBuffer;
  startTime: number;
  endTime: number;
  originalText: string;
  translatedText: string;
  language: string;
}

/**
 * 播放状态
 */
export enum PlaybackState {
  IDLE = 'idle',
  PLAYING = 'playing',
  PAUSED = 'paused',
  STOPPED = 'stopped'
}

/**
 * 音频同步播放器
 */
export class AudioSyncPlayer {
  private audioContext: AudioContext;
  private videoElement: HTMLVideoElement | null = null;
  private audioQueue: IAudioSyncItem[] = [];
  private currentlyPlaying: Map<string, AudioBufferSourceNode> = new Map();
  private state: PlaybackState = PlaybackState.IDLE;
  private syncOffset: number = 0; // 同步偏移量（毫秒）
  private volume: number = 0.8;
  private gainNode: GainNode;
  private isEnabled: boolean = true;

  constructor() {
    this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    this.gainNode = this.audioContext.createGain();
    this.gainNode.connect(this.audioContext.destination);
    this.gainNode.gain.value = this.volume;
  }

  /**
   * 设置视频元素
   */
  setVideoElement(videoElement: HTMLVideoElement): void {
    this.videoElement = videoElement;
    this.setupVideoEventListeners();
  }

  /**
   * 添加音频项到播放队列
   */
  addAudioItem(item: IAudioSyncItem): void {
    // 按时间顺序插入
    const insertIndex = this.audioQueue.findIndex(existing => existing.startTime > item.startTime);
    if (insertIndex === -1) {
      this.audioQueue.push(item);
    } else {
      this.audioQueue.splice(insertIndex, 0, item);
    }

    console.log(`Added audio item: ${item.id} (${item.startTime}s - ${item.endTime}s)`);
  }

  /**
   * 开始同步播放
   */
  start(): void {
    if (!this.videoElement) {
      throw new Error('Video element not set');
    }

    this.state = PlaybackState.PLAYING;
    this.startSyncLoop();
    console.log('Audio sync player started');
  }

  /**
   * 暂停播放
   */
  pause(): void {
    this.state = PlaybackState.PAUSED;
    this.stopAllCurrentAudio();
    console.log('Audio sync player paused');
  }

  /**
   * 停止播放
   */
  stop(): void {
    this.state = PlaybackState.STOPPED;
    this.stopAllCurrentAudio();
    this.audioQueue = [];
    console.log('Audio sync player stopped');
  }

  /**
   * 设置同步偏移量
   */
  setSyncOffset(offsetMs: number): void {
    this.syncOffset = offsetMs;
    console.log(`Sync offset set to: ${offsetMs}ms`);
  }

  /**
   * 设置音量
   */
  setVolume(volume: number): void {
    this.volume = Math.max(0, Math.min(1, volume));
    this.gainNode.gain.value = this.volume;
  }

  /**
   * 启用/禁用播放器
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    if (!enabled) {
      this.stopAllCurrentAudio();
    }
  }

  /**
   * 获取当前状态
   */
  getState(): PlaybackState {
    return this.state;
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stop();
    if (this.audioContext.state !== 'closed') {
      this.audioContext.close();
    }
  }

  /**
   * 设置视频事件监听器
   */
  private setupVideoEventListeners(): void {
    if (!this.videoElement) return;

    // 监听视频播放状态变化
    this.videoElement.addEventListener('play', () => {
      if (this.state === PlaybackState.PAUSED) {
        this.start();
      }
    });

    this.videoElement.addEventListener('pause', () => {
      if (this.state === PlaybackState.PLAYING) {
        this.pause();
      }
    });

    // 监听视频跳转
    this.videoElement.addEventListener('seeked', () => {
      this.handleVideoSeek();
    });

    // 监听视频速度变化
    this.videoElement.addEventListener('ratechange', () => {
      this.handlePlaybackRateChange();
    });
  }

  /**
   * 开始同步循环
   */
  private startSyncLoop(): void {
    const syncLoop = () => {
      if (this.state !== PlaybackState.PLAYING || !this.isEnabled) {
        return;
      }

      this.updateAudioPlayback();
      requestAnimationFrame(syncLoop);
    };

    requestAnimationFrame(syncLoop);
  }

  /**
   * 更新音频播放
   */
  private updateAudioPlayback(): void {
    if (!this.videoElement) return;

    const currentVideoTime = this.videoElement.currentTime;
    const adjustedTime = currentVideoTime + (this.syncOffset / 1000);

    // 查找应该播放的音频项
    const itemsToPlay = this.audioQueue.filter(item => 
      adjustedTime >= item.startTime && 
      adjustedTime <= item.endTime &&
      !this.currentlyPlaying.has(item.id)
    );

    // 播放新的音频项
    itemsToPlay.forEach(item => {
      this.playAudioItem(item, adjustedTime);
    });

    // 停止已结束的音频项
    this.currentlyPlaying.forEach((source, itemId) => {
      const item = this.audioQueue.find(i => i.id === itemId);
      if (!item || adjustedTime > item.endTime) {
        this.stopAudioItem(itemId);
      }
    });
  }

  /**
   * 播放音频项
   */
  private async playAudioItem(item: IAudioSyncItem, currentTime: number): Promise<void> {
    try {
      // 解码音频数据
      const audioBuffer = await this.audioContext.decodeAudioData(item.audioBuffer.slice(0));
      
      // 创建音频源节点
      const source = this.audioContext.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(this.gainNode);

      // 计算播放偏移量
      const playOffset = Math.max(0, currentTime - item.startTime);
      const remainingDuration = Math.max(0, item.endTime - currentTime);

      // 开始播放
      source.start(0, playOffset, remainingDuration);
      this.currentlyPlaying.set(item.id, source);

      // 设置结束回调
      source.onended = () => {
        this.currentlyPlaying.delete(item.id);
      };

      console.log(`Playing audio item: ${item.id} at ${currentTime}s (offset: ${playOffset}s)`);
    } catch (error) {
      console.error(`Failed to play audio item ${item.id}:`, error);
    }
  }

  /**
   * 停止音频项
   */
  private stopAudioItem(itemId: string): void {
    const source = this.currentlyPlaying.get(itemId);
    if (source) {
      try {
        source.stop();
      } catch (error) {
        // 忽略已停止的音频源错误
      }
      this.currentlyPlaying.delete(itemId);
    }
  }

  /**
   * 停止所有当前播放的音频
   */
  private stopAllCurrentAudio(): void {
    this.currentlyPlaying.forEach((source, itemId) => {
      this.stopAudioItem(itemId);
    });
  }

  /**
   * 处理视频跳转
   */
  private handleVideoSeek(): void {
    // 停止所有当前播放的音频
    this.stopAllCurrentAudio();
    
    // 清理过期的音频项（可选）
    if (this.videoElement) {
      const currentTime = this.videoElement.currentTime;
      this.audioQueue = this.audioQueue.filter(item => item.endTime > currentTime - 30); // 保留30秒内的音频
    }
  }

  /**
   * 处理播放速度变化
   */
  private handlePlaybackRateChange(): void {
    if (!this.videoElement) return;

    const playbackRate = this.videoElement.playbackRate;
    
    // 停止当前播放的音频
    this.stopAllCurrentAudio();
    
    // 调整音频播放速度（如果支持）
    // 注意：Web Audio API的AudioBufferSourceNode不支持动态调整播放速度
    // 可能需要使用其他方法或重新生成音频
    console.log(`Video playback rate changed to: ${playbackRate}`);
  }
}

/**
 * TTS音频项构建器
 */
export class TTSAudioItemBuilder {
  /**
   * 从TTS结果创建音频项
   */
  static fromTTSResult(
    ttsResult: ITTSResult,
    startTime: number,
    originalText: string,
    translatedText: string,
    language: string
  ): IAudioSyncItem {
    return {
      id: this.generateId(),
      audioBuffer: ttsResult.audioBuffer,
      startTime,
      endTime: startTime + ttsResult.duration,
      originalText,
      translatedText,
      language
    };
  }

  /**
   * 从字幕条目创建音频项
   */
  static fromSubtitleEntry(
    ttsResult: ITTSResult,
    subtitleEntry: any,
    translatedText: string,
    language: string
  ): IAudioSyncItem {
    return {
      id: this.generateId(),
      audioBuffer: ttsResult.audioBuffer,
      startTime: subtitleEntry.startTime,
      endTime: subtitleEntry.endTime,
      originalText: subtitleEntry.text,
      translatedText,
      language
    };
  }

  private static generateId(): string {
    return `audio_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 音频同步播放器工厂
 */
export class AudioSyncPlayerFactory {
  private static instance: AudioSyncPlayer | null = null;

  /**
   * 获取单例实例
   */
  static getInstance(): AudioSyncPlayer {
    if (!this.instance) {
      this.instance = new AudioSyncPlayer();
    }
    return this.instance;
  }

  /**
   * 重置实例
   */
  static reset(): void {
    if (this.instance) {
      this.instance.cleanup();
      this.instance = null;
    }
  }
}
