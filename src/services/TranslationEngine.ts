import { ITranslationResult } from '../types/media';

/**
 * 翻译引擎接口
 */
export interface ITranslationEngine {
  translate(text: string, targetLanguage: string, sourceLanguage?: string): Promise<ITranslationResult>;
  getSupportedLanguages(): Promise<string[]>;
  detectLanguage(text: string): Promise<string>;
  getName(): string;
  isAvailable(): Promise<boolean>;
}

/**
 * 翻译引擎管理器
 */
export class TranslationEngineManager {
  private engines: Map<string, ITranslationEngine> = new Map();
  private primaryEngine: string | null = null;
  private fallbackEngines: string[] = [];

  /**
   * 注册翻译引擎
   */
  registerEngine(name: string, engine: ITranslationEngine): void {
    this.engines.set(name, engine);
    
    // 设置第一个引擎为主引擎
    if (!this.primaryEngine) {
      this.primaryEngine = name;
    }
  }

  /**
   * 设置主引擎
   */
  setPrimaryEngine(name: string): void {
    if (this.engines.has(name)) {
      this.primaryEngine = name;
    } else {
      throw new Error(`Translation engine '${name}' not found`);
    }
  }

  /**
   * 设置备用引擎
   */
  setFallbackEngines(engines: string[]): void {
    this.fallbackEngines = engines.filter(name => this.engines.has(name));
  }

  /**
   * 翻译文本
   */
  async translate(text: string, targetLanguage: string, sourceLanguage?: string): Promise<ITranslationResult> {
    const engineNames = [this.primaryEngine, ...this.fallbackEngines].filter(Boolean) as string[];
    
    for (const engineName of engineNames) {
      const engine = this.engines.get(engineName);
      if (!engine) continue;

      try {
        const isAvailable = await engine.isAvailable();
        if (!isAvailable) continue;

        const result = await engine.translate(text, targetLanguage, sourceLanguage);
        return result;
      } catch (error) {
        console.warn(`Translation failed with engine ${engineName}:`, error);
        continue;
      }
    }

    throw new Error('All translation engines failed');
  }

  /**
   * 检测语言
   */
  async detectLanguage(text: string): Promise<string> {
    const engine = this.engines.get(this.primaryEngine!);
    if (!engine) {
      throw new Error('No primary translation engine available');
    }

    return engine.detectLanguage(text);
  }

  /**
   * 获取可用引擎列表
   */
  getAvailableEngines(): string[] {
    return Array.from(this.engines.keys());
  }
}

/**
 * Google Translate 引擎
 */
export class GoogleTranslateEngine implements ITranslationEngine {
  private apiKey: string;
  private baseUrl = 'https://translation.googleapis.com/language/translate/v2';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async translate(text: string, targetLanguage: string, sourceLanguage?: string): Promise<ITranslationResult> {
    const params = new URLSearchParams({
      key: this.apiKey,
      q: text,
      target: targetLanguage,
      format: 'text'
    });

    if (sourceLanguage) {
      params.append('source', sourceLanguage);
    }

    const response = await fetch(`${this.baseUrl}?${params}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      }
    });

    if (!response.ok) {
      throw new Error(`Google Translate API error: ${response.status}`);
    }

    const data = await response.json();
    const translation = data.data.translations[0];

    return {
      originalText: text,
      translatedText: translation.translatedText,
      sourceLanguage: translation.detectedSourceLanguage || sourceLanguage || 'auto',
      targetLanguage,
      confidence: 0.9, // Google doesn't provide confidence scores
      provider: 'google'
    };
  }

  async getSupportedLanguages(): Promise<string[]> {
    const response = await fetch(`${this.baseUrl}/languages?key=${this.apiKey}`);
    if (!response.ok) {
      throw new Error(`Failed to get supported languages: ${response.status}`);
    }

    const data = await response.json();
    return data.data.languages.map((lang: any) => lang.language);
  }

  async detectLanguage(text: string): Promise<string> {
    const params = new URLSearchParams({
      key: this.apiKey,
      q: text
    });

    const response = await fetch(`${this.baseUrl}/detect?${params}`, {
      method: 'POST'
    });

    if (!response.ok) {
      throw new Error(`Language detection failed: ${response.status}`);
    }

    const data = await response.json();
    return data.data.detections[0][0].language;
  }

  getName(): string {
    return 'google';
  }

  async isAvailable(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/languages?key=${this.apiKey}`);
      return response.ok;
    } catch {
      return false;
    }
  }
}

/**
 * OpenAI 翻译引擎
 */
export class OpenAITranslateEngine implements ITranslationEngine {
  private apiKey: string;
  private baseUrl = 'https://api.openai.com/v1/chat/completions';
  private model = 'gpt-3.5-turbo';

  constructor(apiKey: string, model = 'gpt-3.5-turbo') {
    this.apiKey = apiKey;
    this.model = model;
  }

  async translate(text: string, targetLanguage: string, sourceLanguage?: string): Promise<ITranslationResult> {
    const prompt = this.buildTranslationPrompt(text, targetLanguage, sourceLanguage);
    
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: this.model,
        messages: [
          {
            role: 'system',
            content: 'You are a professional translator. Translate the given text accurately while preserving the original meaning and tone.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 1000,
        temperature: 0.3
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    const translatedText = data.choices[0].message.content.trim();

    return {
      originalText: text,
      translatedText,
      sourceLanguage: sourceLanguage || 'auto',
      targetLanguage,
      confidence: 0.85,
      provider: 'openai'
    };
  }

  private buildTranslationPrompt(text: string, targetLanguage: string, sourceLanguage?: string): string {
    const sourceLangText = sourceLanguage ? ` from ${sourceLanguage}` : '';
    return `Translate the following text${sourceLangText} to ${targetLanguage}:\n\n"${text}"\n\nTranslation:`;
  }

  async getSupportedLanguages(): Promise<string[]> {
    // OpenAI supports most major languages
    return [
      'en', 'zh', 'ja', 'ko', 'es', 'fr', 'de', 'it', 'pt', 'ru', 
      'ar', 'hi', 'th', 'vi', 'tr', 'pl', 'nl', 'sv', 'da', 'no'
    ];
  }

  async detectLanguage(text: string): Promise<string> {
    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: this.model,
        messages: [
          {
            role: 'user',
            content: `Detect the language of this text and respond with only the ISO 639-1 language code: "${text}"`
          }
        ],
        max_tokens: 10,
        temperature: 0
      })
    });

    if (!response.ok) {
      throw new Error(`Language detection failed: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0].message.content.trim().toLowerCase();
  }

  getName(): string {
    return 'openai';
  }

  async isAvailable(): Promise<boolean> {
    try {
      const response = await fetch('https://api.openai.com/v1/models', {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`
        }
      });
      return response.ok;
    } catch {
      return false;
    }
  }
}

/**
 * 浏览器内置翻译引擎（实验性）
 */
export class BrowserTranslateEngine implements ITranslationEngine {
  getName(): string {
    return 'browser';
  }

  async translate(text: string, targetLanguage: string, sourceLanguage?: string): Promise<ITranslationResult> {
    // 检查浏览器是否支持翻译API
    if (!('translation' in navigator)) {
      throw new Error('Browser translation not supported');
    }

    // 这是一个实验性API，实际实现可能不同
    const translator = await (navigator as any).translation.createTranslator({
      sourceLanguage: sourceLanguage || 'auto',
      targetLanguage
    });

    const translatedText = await translator.translate(text);

    return {
      originalText: text,
      translatedText,
      sourceLanguage: sourceLanguage || 'auto',
      targetLanguage,
      confidence: 0.8,
      provider: 'browser'
    };
  }

  async getSupportedLanguages(): Promise<string[]> {
    if (!('translation' in navigator)) {
      return [];
    }

    const capabilities = await (navigator as any).translation.getCapabilities();
    return capabilities.supportedLanguages || [];
  }

  async detectLanguage(text: string): Promise<string> {
    // 简单的语言检测逻辑
    if (/[\u4e00-\u9fff]/.test(text)) return 'zh';
    if (/[\u3040-\u309f\u30a0-\u30ff]/.test(text)) return 'ja';
    if (/[\uac00-\ud7af]/.test(text)) return 'ko';
    if (/[а-яё]/i.test(text)) return 'ru';
    if (/[α-ωάέήίόύώ]/i.test(text)) return 'el';
    if (/[ا-ي]/i.test(text)) return 'ar';
    
    return 'en'; // 默认英语
  }

  async isAvailable(): Promise<boolean> {
    return 'translation' in navigator;
  }
}
