/**
 * 同步音频项接口
 */
export interface ISyncAudioItem {
  id: string;
  audioBuffer: ArrayBuffer;
  startTime: number;
  endTime: number;
  duration: number;
  priority: number;
  metadata: {
    originalText: string;
    translatedText: string;
    language: string;
    confidence: number;
    processingLatency: number;
  };
}

/**
 * 播放预测信息
 */
export interface IPlaybackPrediction {
  predictedTime: number;
  confidence: number;
  latencyCompensation: number;
  bufferHealth: number;
}

/**
 * 实时同步引擎
 */
export class RealtimeSyncEngine {
  private audioContext: AudioContext;
  private videoElement: HTMLVideoElement | null = null;
  private audioQueue: Map<string, ISyncAudioItem> = new Map();
  private activeAudioSources: Map<string, AudioBufferSourceNode> = new Map();
  private gainNode: GainNode;
  
  // 同步参数
  private syncConfig = {
    lookAheadTime: 0.1, // 预测播放提前时间（秒）
    maxLatencyCompensation: 0.5, // 最大延迟补偿（秒）
    audioFadeTime: 0.05, // 音频淡入淡出时间（秒）
    syncTolerance: 0.02, // 同步容差（秒）
    adaptiveSync: true, // 自适应同步
    qualityThreshold: 0.8 // 音频质量阈值
  };

  // 性能监控
  private performanceMetrics = {
    averageLatency: 0,
    syncAccuracy: 0,
    droppedFrames: 0,
    totalProcessed: 0,
    lastSyncTime: 0
  };

  // 预测模型
  private predictionModel = {
    playbackHistory: [] as number[],
    latencyHistory: [] as number[],
    maxHistorySize: 100
  };

  constructor() {
    this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    this.gainNode = this.audioContext.createGain();
    this.gainNode.connect(this.audioContext.destination);
    this.setupAudioContext();
  }

  /**
   * 设置视频元素
   */
  setVideoElement(videoElement: HTMLVideoElement): void {
    this.videoElement = videoElement;
    this.setupVideoEventListeners();
  }

  /**
   * 添加音频项到同步队列
   */
  addAudioItem(audioItem: ISyncAudioItem): void {
    // 检查音频质量
    if (audioItem.metadata.confidence < this.syncConfig.qualityThreshold) {
      console.warn(`Low quality audio item: ${audioItem.id}, confidence: ${audioItem.metadata.confidence}`);
    }

    this.audioQueue.set(audioItem.id, audioItem);
    
    // 立即检查是否需要播放
    this.checkImmediatePlayback(audioItem);
    
    console.log(`Added audio item: ${audioItem.id} (${audioItem.startTime.toFixed(3)}s - ${audioItem.endTime.toFixed(3)}s)`);
  }

  /**
   * 开始实时同步
   */
  start(): void {
    this.startSyncLoop();
    console.log('Realtime sync engine started');
  }

  /**
   * 停止同步
   */
  stop(): void {
    this.stopAllAudio();
    this.audioQueue.clear();
    console.log('Realtime sync engine stopped');
  }

  /**
   * 开始同步循环
   */
  private startSyncLoop(): void {
    const syncLoop = () => {
      if (!this.videoElement) {
        requestAnimationFrame(syncLoop);
        return;
      }

      const currentTime = this.videoElement.currentTime;
      const prediction = this.predictPlaybackTime(currentTime);
      
      this.updatePlaybackPrediction(prediction);
      this.processAudioQueue(prediction);
      this.updatePerformanceMetrics();
      
      requestAnimationFrame(syncLoop);
    };

    requestAnimationFrame(syncLoop);
  }

  /**
   * 预测播放时间
   */
  private predictPlaybackTime(currentTime: number): IPlaybackPrediction {
    const playbackRate = this.videoElement?.playbackRate || 1;
    const lookAhead = this.syncConfig.lookAheadTime;
    
    // 基础预测时间
    let predictedTime = currentTime + (lookAhead * playbackRate);
    
    // 计算延迟补偿
    const averageLatency = this.calculateAverageLatency();
    const latencyCompensation = Math.min(averageLatency, this.syncConfig.maxLatencyCompensation);
    
    // 应用自适应同步
    if (this.syncConfig.adaptiveSync) {
      const adaptiveOffset = this.calculateAdaptiveOffset();
      predictedTime += adaptiveOffset;
    }

    // 计算缓冲健康度
    const bufferHealth = this.calculateBufferHealth();
    
    // 置信度计算
    const confidence = this.calculatePredictionConfidence(bufferHealth, averageLatency);

    return {
      predictedTime: predictedTime + latencyCompensation,
      confidence,
      latencyCompensation,
      bufferHealth
    };
  }

  /**
   * 处理音频队列
   */
  private processAudioQueue(prediction: IPlaybackPrediction): void {
    const itemsToPlay: ISyncAudioItem[] = [];
    const itemsToStop: string[] = [];

    this.audioQueue.forEach((item, id) => {
      const shouldPlay = this.shouldPlayAudioItem(item, prediction);
      const shouldStop = this.shouldStopAudioItem(item, prediction);

      if (shouldPlay && !this.activeAudioSources.has(id)) {
        itemsToPlay.push(item);
      } else if (shouldStop && this.activeAudioSources.has(id)) {
        itemsToStop.push(id);
      }
    });

    // 播放新音频
    itemsToPlay.forEach(item => {
      this.playAudioItem(item, prediction);
    });

    // 停止过期音频
    itemsToStop.forEach(id => {
      this.stopAudioItem(id);
    });
  }

  /**
   * 判断是否应该播放音频项
   */
  private shouldPlayAudioItem(item: ISyncAudioItem, prediction: IPlaybackPrediction): boolean {
    const { predictedTime } = prediction;
    const tolerance = this.syncConfig.syncTolerance;
    
    return predictedTime >= (item.startTime - tolerance) && 
           predictedTime <= (item.endTime + tolerance);
  }

  /**
   * 判断是否应该停止音频项
   */
  private shouldStopAudioItem(item: ISyncAudioItem, prediction: IPlaybackPrediction): boolean {
    const { predictedTime } = prediction;
    return predictedTime > item.endTime + this.syncConfig.syncTolerance;
  }

  /**
   * 播放音频项
   */
  private async playAudioItem(item: ISyncAudioItem, prediction: IPlaybackPrediction): Promise<void> {
    try {
      const audioBuffer = await this.audioContext.decodeAudioData(item.audioBuffer.slice(0));
      const source = this.audioContext.createBufferSource();
      const itemGain = this.audioContext.createGain();

      source.buffer = audioBuffer;
      source.connect(itemGain);
      itemGain.connect(this.gainNode);

      // 计算播放偏移
      const currentVideoTime = this.videoElement?.currentTime || 0;
      const playOffset = Math.max(0, currentVideoTime - item.startTime);
      const remainingDuration = Math.max(0, item.duration - playOffset);

      // 应用音频淡入
      this.applyAudioFade(itemGain, 'in');

      // 开始播放
      const when = this.audioContext.currentTime;
      source.start(when, playOffset, remainingDuration);
      
      this.activeAudioSources.set(item.id, source);

      // 设置结束回调
      source.onended = () => {
        this.activeAudioSources.delete(item.id);
        this.audioQueue.delete(item.id);
      };

      // 更新性能指标
      this.updatePlaybackMetrics(item, prediction);

      console.log(`Playing audio: ${item.id} at ${currentVideoTime.toFixed(3)}s (offset: ${playOffset.toFixed(3)}s)`);
    } catch (error) {
      console.error(`Failed to play audio item ${item.id}:`, error);
      this.performanceMetrics.droppedFrames++;
    }
  }

  /**
   * 停止音频项
   */
  private stopAudioItem(id: string): void {
    const source = this.activeAudioSources.get(id);
    if (source) {
      try {
        // 应用音频淡出
        const gainNode = source.context.createGain();
        this.applyAudioFade(gainNode, 'out');
        
        source.stop();
      } catch (error) {
        // 忽略已停止的音频源错误
      }
      this.activeAudioSources.delete(id);
    }
  }

  /**
   * 应用音频淡入淡出
   */
  private applyAudioFade(gainNode: GainNode, type: 'in' | 'out'): void {
    const fadeTime = this.syncConfig.audioFadeTime;
    const currentTime = this.audioContext.currentTime;

    if (type === 'in') {
      gainNode.gain.setValueAtTime(0, currentTime);
      gainNode.gain.linearRampToValueAtTime(1, currentTime + fadeTime);
    } else {
      gainNode.gain.setValueAtTime(1, currentTime);
      gainNode.gain.linearRampToValueAtTime(0, currentTime + fadeTime);
    }
  }

  /**
   * 检查立即播放
   */
  private checkImmediatePlayback(item: ISyncAudioItem): void {
    if (!this.videoElement) return;

    const currentTime = this.videoElement.currentTime;
    const tolerance = this.syncConfig.syncTolerance;

    if (currentTime >= (item.startTime - tolerance) && currentTime <= (item.endTime + tolerance)) {
      const prediction = this.predictPlaybackTime(currentTime);
      this.playAudioItem(item, prediction);
    }
  }

  /**
   * 计算平均延迟
   */
  private calculateAverageLatency(): number {
    const { latencyHistory } = this.predictionModel;
    if (latencyHistory.length === 0) return 0;

    const sum = latencyHistory.reduce((acc, val) => acc + val, 0);
    return sum / latencyHistory.length;
  }

  /**
   * 计算自适应偏移
   */
  private calculateAdaptiveOffset(): number {
    const syncAccuracy = this.performanceMetrics.syncAccuracy;
    
    // 基于同步精度调整偏移
    if (syncAccuracy < 0.8) {
      return 0.02; // 增加提前量
    } else if (syncAccuracy > 0.95) {
      return -0.01; // 减少提前量
    }
    
    return 0;
  }

  /**
   * 计算缓冲健康度
   */
  private calculateBufferHealth(): number {
    if (!this.videoElement) return 0;

    const buffered = this.videoElement.buffered;
    const currentTime = this.videoElement.currentTime;
    
    for (let i = 0; i < buffered.length; i++) {
      if (buffered.start(i) <= currentTime && buffered.end(i) > currentTime) {
        const bufferAhead = buffered.end(i) - currentTime;
        return Math.min(bufferAhead / 10, 1); // 10秒为满分
      }
    }
    
    return 0;
  }

  /**
   * 计算预测置信度
   */
  private calculatePredictionConfidence(bufferHealth: number, averageLatency: number): number {
    const bufferScore = bufferHealth;
    const latencyScore = Math.max(0, 1 - (averageLatency / 0.5));
    const historyScore = Math.min(this.predictionModel.playbackHistory.length / 50, 1);
    
    return (bufferScore + latencyScore + historyScore) / 3;
  }

  /**
   * 更新播放预测
   */
  private updatePlaybackPrediction(prediction: IPlaybackPrediction): void {
    const { playbackHistory } = this.predictionModel;
    
    playbackHistory.push(prediction.predictedTime);
    
    if (playbackHistory.length > this.predictionModel.maxHistorySize) {
      playbackHistory.shift();
    }
  }

  /**
   * 更新播放指标
   */
  private updatePlaybackMetrics(item: ISyncAudioItem, prediction: IPlaybackPrediction): void {
    const processingLatency = item.metadata.processingLatency;
    const { latencyHistory } = this.predictionModel;
    
    latencyHistory.push(processingLatency);
    
    if (latencyHistory.length > this.predictionModel.maxHistorySize) {
      latencyHistory.shift();
    }
    
    this.performanceMetrics.totalProcessed++;
    this.performanceMetrics.lastSyncTime = Date.now();
  }

  /**
   * 更新性能指标
   */
  private updatePerformanceMetrics(): void {
    this.performanceMetrics.averageLatency = this.calculateAverageLatency();
    
    // 计算同步精度（简化版）
    const activeCount = this.activeAudioSources.size;
    const queueCount = this.audioQueue.size;
    this.performanceMetrics.syncAccuracy = activeCount > 0 ? 
      Math.min(activeCount / Math.max(queueCount, 1), 1) : 1;
  }

  /**
   * 设置音频上下文
   */
  private setupAudioContext(): void {
    // 处理浏览器自动播放策略
    document.addEventListener('click', () => {
      if (this.audioContext.state === 'suspended') {
        this.audioContext.resume();
      }
    }, { once: true });
  }

  /**
   * 设置视频事件监听器
   */
  private setupVideoEventListeners(): void {
    if (!this.videoElement) return;

    this.videoElement.addEventListener('pause', () => {
      this.stopAllAudio();
    });

    this.videoElement.addEventListener('seeked', () => {
      this.handleVideoSeek();
    });

    this.videoElement.addEventListener('ratechange', () => {
      this.handlePlaybackRateChange();
    });
  }

  /**
   * 处理视频跳转
   */
  private handleVideoSeek(): void {
    this.stopAllAudio();
    
    // 清理不相关的音频项
    const currentTime = this.videoElement?.currentTime || 0;
    const itemsToRemove: string[] = [];

    this.audioQueue.forEach((item, id) => {
      if (item.endTime < currentTime - 5 || item.startTime > currentTime + 30) {
        itemsToRemove.push(id);
      }
    });

    itemsToRemove.forEach(id => {
      this.audioQueue.delete(id);
    });
  }

  /**
   * 处理播放速度变化
   */
  private handlePlaybackRateChange(): void {
    const playbackRate = this.videoElement?.playbackRate || 1;
    
    // 调整同步参数
    this.syncConfig.lookAheadTime = Math.max(0.05, 0.1 / playbackRate);
    
    console.log(`Playback rate changed to ${playbackRate}x`);
  }

  /**
   * 停止所有音频
   */
  private stopAllAudio(): void {
    this.activeAudioSources.forEach((source, id) => {
      this.stopAudioItem(id);
    });
  }

  /**
   * 获取性能指标
   */
  getPerformanceMetrics(): typeof this.performanceMetrics {
    return { ...this.performanceMetrics };
  }

  /**
   * 更新同步配置
   */
  updateSyncConfig(newConfig: Partial<typeof this.syncConfig>): void {
    this.syncConfig = { ...this.syncConfig, ...newConfig };
    console.log('Sync config updated:', this.syncConfig);
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stop();
    if (this.audioContext.state !== 'closed') {
      this.audioContext.close();
    }
  }
}
