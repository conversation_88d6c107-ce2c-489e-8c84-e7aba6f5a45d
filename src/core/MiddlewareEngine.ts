/**
 * 中间件上下文接口
 */
export interface IMiddlewareContext {
  data: any;
  metadata: {
    timestamp: number;
    videoTime: number;
    processingId: string;
    priority: number;
    retryCount: number;
  };
  state: Map<string, any>;
  abort: AbortController;
}

/**
 * 中间件接口
 */
export interface IMiddleware {
  name: string;
  priority: number;
  enabled: boolean;
  
  /**
   * 处理数据
   */
  process(context: IMiddlewareContext, next: () => Promise<void>): Promise<void>;
  
  /**
   * 错误处理
   */
  onError?(error: Error, context: IMiddlewareContext): Promise<void>;
  
  /**
   * 初始化
   */
  initialize?(): Promise<void>;
  
  /**
   * 清理
   */
  cleanup?(): Promise<void>;
}

/**
 * 中间件引擎
 */
export class MiddlewareEngine {
  private middlewares: IMiddleware[] = [];
  private isRunning = false;
  private processingQueue: IMiddlewareContext[] = [];
  private concurrentLimit = 3;
  private activeProcessing = new Set<string>();

  /**
   * 注册中间件
   */
  use(middleware: IMiddleware): void {
    this.middlewares.push(middleware);
    // 按优先级排序
    this.middlewares.sort((a, b) => a.priority - b.priority);
  }

  /**
   * 移除中间件
   */
  remove(name: string): void {
    this.middlewares = this.middlewares.filter(m => m.name !== name);
  }

  /**
   * 启用/禁用中间件
   */
  toggle(name: string, enabled: boolean): void {
    const middleware = this.middlewares.find(m => m.name === name);
    if (middleware) {
      middleware.enabled = enabled;
    }
  }

  /**
   * 处理数据
   */
  async process(data: any, metadata: Partial<IMiddlewareContext['metadata']> = {}): Promise<any> {
    const context: IMiddlewareContext = {
      data,
      metadata: {
        timestamp: Date.now(),
        videoTime: 0,
        processingId: this.generateId(),
        priority: 0,
        retryCount: 0,
        ...metadata
      },
      state: new Map(),
      abort: new AbortController()
    };

    // 检查并发限制
    if (this.activeProcessing.size >= this.concurrentLimit) {
      this.processingQueue.push(context);
      await this.waitForSlot();
    }

    this.activeProcessing.add(context.metadata.processingId);

    try {
      await this.executeMiddlewareChain(context);
      return context.data;
    } finally {
      this.activeProcessing.delete(context.metadata.processingId);
      this.processQueue();
    }
  }

  /**
   * 执行中间件链
   */
  private async executeMiddlewareChain(context: IMiddlewareContext): Promise<void> {
    const enabledMiddlewares = this.middlewares.filter(m => m.enabled);
    let index = 0;

    const next = async (): Promise<void> => {
      if (index >= enabledMiddlewares.length) return;
      
      const middleware = enabledMiddlewares[index++];
      
      try {
        await middleware.process(context, next);
      } catch (error) {
        if (middleware.onError) {
          await middleware.onError(error as Error, context);
        } else {
          throw error;
        }
      }
    };

    await next();
  }

  /**
   * 等待处理槽位
   */
  private async waitForSlot(): Promise<void> {
    return new Promise((resolve) => {
      const checkSlot = () => {
        if (this.activeProcessing.size < this.concurrentLimit) {
          resolve();
        } else {
          setTimeout(checkSlot, 10);
        }
      };
      checkSlot();
    });
  }

  /**
   * 处理队列
   */
  private processQueue(): void {
    if (this.processingQueue.length > 0 && this.activeProcessing.size < this.concurrentLimit) {
      const context = this.processingQueue.shift();
      if (context) {
        this.process(context.data, context.metadata);
      }
    }
  }

  /**
   * 初始化所有中间件
   */
  async initialize(): Promise<void> {
    for (const middleware of this.middlewares) {
      if (middleware.initialize) {
        await middleware.initialize();
      }
    }
    this.isRunning = true;
  }

  /**
   * 清理所有中间件
   */
  async cleanup(): Promise<void> {
    this.isRunning = false;
    
    // 取消所有正在处理的任务
    this.activeProcessing.forEach(id => {
      // 这里可以添加取消逻辑
    });

    for (const middleware of this.middlewares) {
      if (middleware.cleanup) {
        await middleware.cleanup();
      }
    }
  }

  /**
   * 设置并发限制
   */
  setConcurrentLimit(limit: number): void {
    this.concurrentLimit = limit;
  }

  /**
   * 获取中间件状态
   */
  getStatus(): MiddlewareEngineStatus {
    return {
      isRunning: this.isRunning,
      middlewareCount: this.middlewares.length,
      enabledMiddlewareCount: this.middlewares.filter(m => m.enabled).length,
      activeProcessing: this.activeProcessing.size,
      queueLength: this.processingQueue.length,
      concurrentLimit: this.concurrentLimit
    };
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `proc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * 中间件引擎状态
 */
export interface MiddlewareEngineStatus {
  isRunning: boolean;
  middlewareCount: number;
  enabledMiddlewareCount: number;
  activeProcessing: number;
  queueLength: number;
  concurrentLimit: number;
}

/**
 * 抽象中间件基类
 */
export abstract class BaseMiddleware implements IMiddleware {
  public abstract name: string;
  public priority: number = 100;
  public enabled: boolean = true;

  abstract process(context: IMiddlewareContext, next: () => Promise<void>): Promise<void>;

  async onError(error: Error, context: IMiddlewareContext): Promise<void> {
    console.error(`[${this.name}] Middleware error:`, error);
    throw error;
  }

  async initialize(): Promise<void> {
    console.log(`[${this.name}] Middleware initialized`);
  }

  async cleanup(): Promise<void> {
    console.log(`[${this.name}] Middleware cleaned up`);
  }

  /**
   * 设置上下文状态
   */
  protected setState(context: IMiddlewareContext, key: string, value: any): void {
    context.state.set(`${this.name}.${key}`, value);
  }

  /**
   * 获取上下文状态
   */
  protected getState<T>(context: IMiddlewareContext, key: string): T | undefined {
    return context.state.get(`${this.name}.${key}`);
  }

  /**
   * 检查是否应该跳过处理
   */
  protected shouldSkip(context: IMiddlewareContext): boolean {
    return context.abort.signal.aborted;
  }
}

/**
 * 中间件工厂
 */
export class MiddlewareFactory {
  private static registry: Map<string, new (...args: any[]) => IMiddleware> = new Map();

  /**
   * 注册中间件类
   */
  static register(name: string, middlewareClass: new (...args: any[]) => IMiddleware): void {
    this.registry.set(name, middlewareClass);
  }

  /**
   * 创建中间件实例
   */
  static create(name: string, ...args: any[]): IMiddleware | null {
    const MiddlewareClass = this.registry.get(name);
    if (MiddlewareClass) {
      return new MiddlewareClass(...args);
    }
    return null;
  }

  /**
   * 获取已注册的中间件列表
   */
  static getRegistered(): string[] {
    return Array.from(this.registry.keys());
  }
}
