/**
 * 视频缓冲信息接口
 */
export interface IVideoBufferInfo {
  currentTime: number;
  bufferedRanges: TimeRanges;
  duration: number;
  playbackRate: number;
  readyState: number;
  networkState: number;
  bufferedAhead: number; // 当前位置之后的缓冲时长
  bufferedBehind: number; // 当前位置之前的缓冲时长
}

/**
 * 预处理任务接口
 */
export interface IPreprocessTask {
  id: string;
  startTime: number;
  endTime: number;
  priority: number;
  type: 'subtitle' | 'audio';
  data: any;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  createdAt: number;
  processedAt?: number;
}

/**
 * 视频缓冲监控器
 */
export class VideoBufferMonitor {
  private videoElement: HTMLVideoElement | null = null;
  private monitoringInterval: number | null = null;
  private bufferInfo: IVideoBufferInfo | null = null;
  private preprocessTasks: Map<string, IPreprocessTask> = new Map();
  private onBufferUpdate?: (info: IVideoBufferInfo) => void;
  private onPreprocessNeeded?: (task: IPreprocessTask) => void;
  
  // 配置参数
  private config = {
    monitorInterval: 100, // 监控间隔（毫秒）
    preprocessAhead: 10, // 提前预处理时间（秒）
    minBufferAhead: 5, // 最小前向缓冲（秒）
    maxPreprocessTasks: 20, // 最大预处理任务数
    taskTimeout: 30000 // 任务超时时间（毫秒）
  };

  /**
   * 设置视频元素
   */
  setVideoElement(videoElement: HTMLVideoElement): void {
    this.videoElement = videoElement;
    this.setupVideoEventListeners();
  }

  /**
   * 开始监控
   */
  startMonitoring(): void {
    if (this.monitoringInterval) return;

    this.monitoringInterval = window.setInterval(() => {
      this.updateBufferInfo();
      this.checkPreprocessNeeds();
      this.cleanupExpiredTasks();
    }, this.config.monitorInterval);

    console.log('Video buffer monitoring started');
  }

  /**
   * 停止监控
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    console.log('Video buffer monitoring stopped');
  }

  /**
   * 设置缓冲更新回调
   */
  onBufferInfoUpdate(callback: (info: IVideoBufferInfo) => void): void {
    this.onBufferUpdate = callback;
  }

  /**
   * 设置预处理需求回调
   */
  onPreprocessTaskNeeded(callback: (task: IPreprocessTask) => void): void {
    this.onPreprocessNeeded = callback;
  }

  /**
   * 更新缓冲信息
   */
  private updateBufferInfo(): void {
    if (!this.videoElement) return;

    const currentTime = this.videoElement.currentTime;
    const bufferedRanges = this.videoElement.buffered;
    
    // 计算前向和后向缓冲
    let bufferedAhead = 0;
    let bufferedBehind = 0;

    for (let i = 0; i < bufferedRanges.length; i++) {
      const start = bufferedRanges.start(i);
      const end = bufferedRanges.end(i);

      if (start <= currentTime && end > currentTime) {
        bufferedAhead = end - currentTime;
      }
      
      if (end <= currentTime) {
        bufferedBehind += end - start;
      }
    }

    this.bufferInfo = {
      currentTime,
      bufferedRanges,
      duration: this.videoElement.duration,
      playbackRate: this.videoElement.playbackRate,
      readyState: this.videoElement.readyState,
      networkState: this.videoElement.networkState,
      bufferedAhead,
      bufferedBehind
    };

    // 触发回调
    if (this.onBufferUpdate) {
      this.onBufferUpdate(this.bufferInfo);
    }
  }

  /**
   * 检查预处理需求
   */
  private checkPreprocessNeeds(): void {
    if (!this.bufferInfo || !this.videoElement) return;

    const { currentTime, bufferedAhead } = this.bufferInfo;
    
    // 如果缓冲足够，创建预处理任务
    if (bufferedAhead >= this.config.minBufferAhead) {
      const preprocessEndTime = currentTime + Math.min(bufferedAhead, this.config.preprocessAhead);
      this.createPreprocessTasks(currentTime, preprocessEndTime);
    }
  }

  /**
   * 创建预处理任务
   */
  private createPreprocessTasks(startTime: number, endTime: number): void {
    // 检查是否已有覆盖此时间段的任务
    const existingTasks = Array.from(this.preprocessTasks.values()).filter(task =>
      task.startTime <= endTime && task.endTime >= startTime
    );

    if (existingTasks.length > 0) return;

    // 限制任务数量
    if (this.preprocessTasks.size >= this.config.maxPreprocessTasks) {
      this.cleanupOldestTasks();
    }

    // 创建字幕预处理任务
    const subtitleTask: IPreprocessTask = {
      id: this.generateTaskId(),
      startTime,
      endTime,
      priority: 1,
      type: 'subtitle',
      data: { timeRange: { start: startTime, end: endTime } },
      status: 'pending',
      createdAt: Date.now()
    };

    // 创建音频预处理任务
    const audioTask: IPreprocessTask = {
      id: this.generateTaskId(),
      startTime,
      endTime,
      priority: 2,
      type: 'audio',
      data: { timeRange: { start: startTime, end: endTime } },
      status: 'pending',
      createdAt: Date.now()
    };

    this.preprocessTasks.set(subtitleTask.id, subtitleTask);
    this.preprocessTasks.set(audioTask.id, audioTask);

    // 触发预处理回调
    if (this.onPreprocessNeeded) {
      this.onPreprocessNeeded(subtitleTask);
      this.onPreprocessNeeded(audioTask);
    }

    console.log(`Created preprocess tasks for time range: ${startTime.toFixed(2)}s - ${endTime.toFixed(2)}s`);
  }

  /**
   * 标记任务状态
   */
  updateTaskStatus(taskId: string, status: IPreprocessTask['status']): void {
    const task = this.preprocessTasks.get(taskId);
    if (task) {
      task.status = status;
      if (status === 'completed' || status === 'failed') {
        task.processedAt = Date.now();
      }
    }
  }

  /**
   * 获取待处理任务
   */
  getPendingTasks(): IPreprocessTask[] {
    return Array.from(this.preprocessTasks.values())
      .filter(task => task.status === 'pending')
      .sort((a, b) => a.priority - b.priority);
  }

  /**
   * 清理过期任务
   */
  private cleanupExpiredTasks(): void {
    const now = Date.now();
    const expiredTasks: string[] = [];

    this.preprocessTasks.forEach((task, id) => {
      // 清理超时任务
      if (now - task.createdAt > this.config.taskTimeout) {
        expiredTasks.push(id);
      }
      
      // 清理已完成且过时的任务
      if (task.status === 'completed' && this.bufferInfo) {
        if (task.endTime < this.bufferInfo.currentTime - 30) { // 30秒前的任务
          expiredTasks.push(id);
        }
      }
    });

    expiredTasks.forEach(id => {
      this.preprocessTasks.delete(id);
    });

    if (expiredTasks.length > 0) {
      console.log(`Cleaned up ${expiredTasks.length} expired preprocess tasks`);
    }
  }

  /**
   * 清理最旧的任务
   */
  private cleanupOldestTasks(): void {
    const tasks = Array.from(this.preprocessTasks.entries())
      .sort(([, a], [, b]) => a.createdAt - b.createdAt);

    const toRemove = Math.ceil(this.config.maxPreprocessTasks * 0.2); // 移除20%最旧的任务
    
    for (let i = 0; i < toRemove && i < tasks.length; i++) {
      this.preprocessTasks.delete(tasks[i][0]);
    }
  }

  /**
   * 设置视频事件监听器
   */
  private setupVideoEventListeners(): void {
    if (!this.videoElement) return;

    // 监听播放状态变化
    this.videoElement.addEventListener('play', () => {
      this.startMonitoring();
    });

    this.videoElement.addEventListener('pause', () => {
      // 暂停时不停止监控，继续预处理
    });

    // 监听跳转
    this.videoElement.addEventListener('seeked', () => {
      this.handleVideoSeek();
    });

    // 监听速度变化
    this.videoElement.addEventListener('ratechange', () => {
      this.handlePlaybackRateChange();
    });

    // 监听缓冲事件
    this.videoElement.addEventListener('progress', () => {
      this.updateBufferInfo();
    });
  }

  /**
   * 处理视频跳转
   */
  private handleVideoSeek(): void {
    // 清理不相关的预处理任务
    const currentTime = this.videoElement?.currentTime || 0;
    const tasksToRemove: string[] = [];

    this.preprocessTasks.forEach((task, id) => {
      if (task.endTime < currentTime - 5 || task.startTime > currentTime + 30) {
        tasksToRemove.push(id);
      }
    });

    tasksToRemove.forEach(id => {
      this.preprocessTasks.delete(id);
    });

    console.log(`Video seeked to ${currentTime.toFixed(2)}s, cleaned up ${tasksToRemove.length} tasks`);
  }

  /**
   * 处理播放速度变化
   */
  private handlePlaybackRateChange(): void {
    const playbackRate = this.videoElement?.playbackRate || 1;
    
    // 调整预处理提前时间
    this.config.preprocessAhead = Math.max(5, 10 / playbackRate);
    
    console.log(`Playback rate changed to ${playbackRate}x, adjusted preprocess ahead to ${this.config.preprocessAhead}s`);
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取当前缓冲信息
   */
  getCurrentBufferInfo(): IVideoBufferInfo | null {
    return this.bufferInfo;
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<typeof this.config>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('Video buffer monitor config updated:', this.config);
  }

  /**
   * 获取统计信息
   */
  getStats(): {
    totalTasks: number;
    pendingTasks: number;
    processingTasks: number;
    completedTasks: number;
    failedTasks: number;
    bufferInfo: IVideoBufferInfo | null;
  } {
    const tasks = Array.from(this.preprocessTasks.values());
    
    return {
      totalTasks: tasks.length,
      pendingTasks: tasks.filter(t => t.status === 'pending').length,
      processingTasks: tasks.filter(t => t.status === 'processing').length,
      completedTasks: tasks.filter(t => t.status === 'completed').length,
      failedTasks: tasks.filter(t => t.status === 'failed').length,
      bufferInfo: this.bufferInfo
    };
  }
}
